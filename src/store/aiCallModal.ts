import { openAICallModal as openModal } from '../components/AICallModal/AICallModal';

import { useSendMessage } from '@/hooks/useSendMessage';
import { EntryPoint } from '@/types';

export const useAICallModalStore = () => {
    const { send } = useSendMessage();
    return (params: any = {}) => {
        // 使用函数式方式打开弹窗，并传入成功回调
        return openModal(params, (taskData) => {
            // 成功回调：发送hideSpan消息
            if (send) {
                const hideSpanContent = JSON.stringify(taskData);
                send(
                    JSON.stringify([
                        { type: 'hideSpan', insert: hideSpanContent },
                    ]),
                    4,
                    EntryPoint.AI_call_modal,
                ); // EntryPointType.TOOL = 4
            }
        });
    };
};
