import useSWR from 'swr';

import { useBizInfo } from './useBizInfo';

import useCallerRequest from '@/hooks/useCallerRequest';
import {
    HomePageDialogueResponse,
    HomePageDialogueData,
    UseHomePageDataReturn,
} from '@/types/homePageApi';
import { getMSILocation } from '@/utils/getLocation';

/**
 * 主页数据获取Hook
 * 使用SWR进行数据缓存，避免重复请求，提升性能
 * 统一处理loading、error、数据刷新等状态
 */
export const useHomePageData = (): UseHomePageDataReturn => {
    const callerRequest = useCallerRequest();
    const { bizId } = useBizInfo();

    // SWR fetcher函数
    const fetcher = async (url: string): Promise<HomePageDialogueData> => {
        try {
            // 获取用户位置信息
            const location = await getMSILocation('bee_assistant');

            const response: HomePageDialogueResponse = await callerRequest.get(
                url,
                {
                    bizId: bizId || 5001,
                    lat: location?.latitude,
                    lng: location?.longitude,
                },
            );

            console.log(
                'BEE_ASSISTANT_DEBUG',
                '获取配置数据成功:',
                bizId,
                JSON.stringify(response),
            );

            // 处理API响应结构 - 检查code、data、msg字段
            if (response?.code === 0 && response?.data) {
                return response.data;
            }

            // 如果请求失败，记录错误信息
            if (response?.code !== 0) {
                console.warn(
                    'Homepage API returned error:',
                    response?.msg || 'Unknown error',
                );
            }

            // 如果没有数据或请求失败，返回空的默认结构
            return {
                skillGroups: [],
                weatherGreeting: {
                    greeting: '',
                    weatherTips: '',
                    weatherType: 'default',
                },
                banner: {
                    bannerContent: '',
                    bannerButton: '',
                    triggerQuestion: '',
                    type: 'default',
                },
            };
        } catch (error) {
            console.error('Failed to fetch homepage data:', error);
            throw error;
        }
    };

    const { data, error, isLoading, mutate } = useSWR<HomePageDialogueData>(
        '/bee/v2/bdaiassistant/homePage/dialogue',
        fetcher,
        {
            revalidateOnFocus: false, // 窗口聚焦时不重新验证
            revalidateOnReconnect: true, // 重新连接时重新验证
            dedupingInterval: 30000, // 30秒内去重
            errorRetryCount: 2, // 错误重试次数
            errorRetryInterval: 1000, // 重试间隔1秒
            shouldRetryOnError: true, // 发生错误时是否重试
        },
    );

    return {
        homePageData: data,
        skillGroups: data?.skillGroups || [],
        weatherGreeting: data?.weatherGreeting,
        banner: data?.banner,
        isLoading,
        error,
        refresh: mutate,
    };
};

/**
 * 检查是否有Banner数据需要显示
 */
export const useBannerVisible = (): boolean => {
    const { banner } = useHomePageData();
    return !!(banner?.bannerContent && banner?.bannerButton);
};

/**
 * 检查是否有问候语数据需要显示
 */
export const useGreetingVisible = (): boolean => {
    const { weatherGreeting } = useHomePageData();
    return !!(weatherGreeting?.greeting || weatherGreeting?.weatherTips);
};
