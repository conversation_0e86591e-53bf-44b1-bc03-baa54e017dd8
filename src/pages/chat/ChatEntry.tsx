import React from 'react';

import ChatNew from './ChatNew';

// let pike;
// try {
//     pike = msi.createPike('com.sankuai.wmbdaiassistant.server');
//     console.log('BEE_ASSISTANT_DEBUG', '创建 Pike 实例成功');
// } catch (error) {
//     console.error('BEE_ASSISTANT_DEBUG', '创建 Pike 实例时发生错误：', error);
// }

// //  通过pike上下文对象调用相关API
// pike.initClient({
//     alias: 'lichen87',
//     success: function (res) {
//         console.log(
//             'BEE_ASSISTANT_DEBUG',
//             'initClient success',
//             JSON.stringify(res),
//         );
//     },
//     fail: function (err) {
//         console.log(
//             'BEE_ASSISTANT_DEBUG',
//             'initClient fail',
//             JSON.stringify(err),
//         );
//     },
// });

// pike.startClient({
//     success: function (res) {
//         console.log(
//             'BEE_ASSISTANT_DEBUG',
//             'startClient success',
//             JSON.stringify(res),
//         );
//     },
//     fail: function (err) {
//         console.log(
//             'BEE_ASSISTANT_DEBUG',
//             'startClient fail',
//             JSON.stringify(err),
//         );
//     },
// });

// pike.onMessageListener({
//     handle: function (res) {
//         console.log('BEE_ASSISTANT_DEBUG', JSON.stringify(res));
//     },
//     success: function (res) {
//         console.log('BEE_ASSISTANT_DEBUG', JSON.stringify(res));
//     },
//     fail: function (err) {
//         console.log('BEE_ASSISTANT_DEBUG', JSON.stringify(err));
//     },
// });

// 如果业务不存在后台监听场景，推荐业务在页面销毁时主动释放资源
// pike.releaseClient({
//     success: function (res) {
//         console.log(JSON.stringify(res));
//     },
//     fail: function (err) {
//         console.log(JSON.stringify(err));
//     },
// });

const ChatEntry = (props: any) => {
    return <ChatNew {...props} />;
};

export default ChatEntry;
