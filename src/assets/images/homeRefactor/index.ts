const BaseUrl = 'https://s3plus.meituan.net/bdaiassistant-public/rn_assets';

const query = '?v=1.0';
// 统一导出对象
const NetImages = {
    // 主目录
    dragIcon: BaseUrl + '/homeRefactor/拖动***********',
    cutIcon: BaseUrl + '/homeRefactor/close.png',
    groupIcon: BaseUrl + '/homeRefactor/group.png',
    whiteAddIcon: BaseUrl + '/homeRefactor/menu_white.png',
    blackAddIcon: BaseUrl + '/homeRefactor/default/更多@3x.png',
    defaultImage: BaseUrl + '/common/defaultImage.png',

    whiteKeyboardIcon: BaseUrl + '/homeRefactor/keyboard_white.png',

    uploadImageIcon: BaseUrl + '/homeRefactor/actionPanel/相册@3x.png',

    // actionPanel
    cameraIcon: BaseUrl + '/homeRefactor/actionPanel/相机@3x.png',
    albumIcon: BaseUrl + '/homeRefactor/actionPanel/相册@3x.png',
    fileIcon: BaseUrl + '/homeRefactor/actionPanel/文件@3x.png',
    merchantIcon: BaseUrl + '/homeRefactor/actionPanel/商家@3x.png',

    // default
    voiceIcon: BaseUrl + '/homeRefactor/default/语音@3x.png' + query,

    // background
    rainBackground: BaseUrl + '/homeRefactor/background/rain.png',
    defaultBackground: BaseUrl + '/common/purple/defaultBg.png',
    coldBackground: BaseUrl + '/homeRefactor/background/cold.png',
    hotBackground: BaseUrl + '/homeRefactor/background/hot.png',
    sendIcon: BaseUrl + '/common/purple/send.png',
    refreshIcon: BaseUrl + '/common/purple/refresh.png',

    defaultIcon: BaseUrl + '/homeRefactor/icon/defaultWithoutText.gif',
    defaultGifIcon: BaseUrl + '/homeRefactor/icon/default.gif',
    defaultIconWithoutSpace:
        BaseUrl + '/homeRefactor/icon/ defaultWithoutSpace.png',
    rainIcon: BaseUrl + '/homeRefactor/icon/rain.png',
    coldIcon: BaseUrl + '/homeRefactor/icon/cold.png',
    hotIcon: BaseUrl + '/homeRefactor/icon/hot.png',
    miniIcon: BaseUrl + '/homeRefactor/icon/mini.png',

    // 技能图标
    defaultSkillIcon: BaseUrl + '/homeRefactor/defaultSkillIcon.png',

    closeCircleBlack: BaseUrl + '/homeRefactor/close_circle_black.png',
    closeCircleWhite: BaseUrl + '/homeRefactor/close_circle_white.png',

    yellowBall: BaseUrl + '/common/yellow_ball.png',
    historyIcon: BaseUrl + '/common/purple/history.png',
    arrowRight: BaseUrl + '/common/arrow_right.png',
    stopIcon: BaseUrl + '/common/purple/stop.png',
    copyIcon: BaseUrl + '/common/copy.png',
    kmIcon: BaseUrl + '/common/km.png',
    kmWithoutTextIcon: BaseUrl + '/common/km_without_text.png',
    tips: BaseUrl + '/common/tips.png',

    sortNormal: BaseUrl + '/common/sort_normal.png',
    sortDown: BaseUrl + '/common/sort_down.png',
    sortUp: BaseUrl + '/common/sort_up.png',

    taskSuccess: BaseUrl + '/common/task_success.png',
    taskNormal: BaseUrl + '/common/task_normal.png',
    taskRunning: BaseUrl + '/common/task_running.png',
    star: BaseUrl + '/common/star.png',

    bannerBg: BaseUrl + '/common/purple/banner_bg.png',
    tipsIcon: BaseUrl + '/common/purple/tips.png',

    down: BaseUrl + '/common/down.png',
    right: BaseUrl + '/common/right.png',
};

export default NetImages;
