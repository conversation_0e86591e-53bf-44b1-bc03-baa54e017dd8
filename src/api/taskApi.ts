import { apiCaller, BFFResponse } from '@mfe/cc-api-caller-bee';
import { useMemo } from 'react';

import useCallerRequest from '../hooks/useCallerRequest';

/**
 * 任务状态接口响应
 */
export interface TaskStatusResponse {
    /** 正在运行中的任务个数 */
    runnings: number;
    /** 是否有需要点击查看的任务 */
    needToClick: boolean;
    /** 需要提醒的任务名称列表 */
    notifyJobNames?: string[];
    /** 是否展示完成图标 */
    showCompleted: boolean;
}

/**
 * 任务类型
 */
export type TaskType = 'PoiDiagnosis' | 'AiCall';

/**
 * 任务状态
 */
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed';

/**
 * 任务项接口 - 根据PRD更新
 */
export interface TaskItem {
    /** 任务类型 */
    type: string;
    /** 商家ID */
    poiId: number;
    /** 商家名称 */
    poiName: string;
    /** 商家头像 */
    poiAvator: string;
    /** 任务状态 success: 成功；fail : 失败；init: 运行中；*/
    status: string;
    /** 能力类型 */
    abilityType: number;
    /** 操作类型 */
    operationType: number;
    /** 内容 */
    content: string;
    /** 任务项id */
    id: number;
    /** 是否展示红点 */
    showRedDot: boolean;
    statusText: string; // 状态文本，由后端直接返回
    statusTextColor?: string; // 状态文本颜色
}

/**
 * 任务作业接口 - 根据PRD定义
 */
export interface TaskJob {
    type: TaskType; // 任务类型
    status: TaskStatus; // 任务状态
    jobId: string; // 任务ID，jobId+type确定唯一任务
    jobName: string; // 任务名称
    createTime: number; // 创建时间戳
    completeTime?: number; // 完成时间戳
    poiNum: number; // 商家个数
    agentName?: string; // agent名称，type=AiCall时有值
    operationType?: number; // 操作类型：1-url跳转，2-继续提问
    content?: string; // 操作内容：url或提问query
    itemList?: TaskItem[]; // 子任务列表，仅商家诊断任务有值
    showRedDot?: boolean;
    id?: number;
}

/**
 * 任务组接口 - 保持向后兼容
 */
export interface TaskGroup {
    /** 任务类型 */
    type: string;
    /** 创建时间 */
    createTime: string;
    /** 任务名称 */
    jobName: string;
    itemList: TaskItem[];
}

/**
 * 任务列表接口响应 - 根据PRD更新
 */
export interface TaskListResponse {
    code: number; // 响应码，0表示成功
    msg: string; // 错误信息
    data: {
        jobList: TaskJob[];
    };
}

/**
 * 饼图数据接口 - 根据PRD定义
 */
export interface PieChartData {
    label: string; // 标签名称
    value: number; // 数值
    color?: string; // 自定义颜色
}

/**
 * 饼图消息接口 - 根据PRD定义
 */
export interface PieChartMessage {
    type: 'pieChart';
    insert: {
        pieChart: {
            title?: string; // 图表标题
            data: PieChartData[];
            showLegend?: boolean; // 是否显示图例，默认true
            showPercent?: boolean; // 是否显示百分比，默认true
        };
    };
}

/**
 * API响应基础结构
 */
interface ApiResponse<T> {
    code: number;
    msg: string;
    data?: T;
}

/**
 * 获取任务状态
 * @returns 任务状态数据
 */
export const getTaskStatus = async (): Promise<TaskStatusResponse | null> => {
    try {
        const res: ApiResponse<TaskStatusResponse> = await apiCaller.get(
            '/bee/v2/bdaiassistant/job/countRunningJobs',
            {},
            { silent: true },
        );

        if (res.code === 0 && res.data) {
            return res.data;
        }

        return null;
    } catch (error) {
        console.error('获取任务状态失败:', error);
        return null;
    }
};

/**
 * 获取任务列表 - 支持类型筛选
 * @param taskType 任务类型筛选：'ALL'、'PoiDiagnosis'、'AiCall'，不传则返回全部
 * @param bizId 业务ID
 * @returns 任务列表数据
 */
export const getTaskList = async (
    taskType?: 'ALL' | TaskType,
    bizId?: string,
): Promise<TaskListResponse | null> => {
    try {
        const params: any = {};
        if (taskType) {
            params.type = taskType;
        }
        if (bizId) {
            params.bizId = bizId;
        }
        const res: ApiResponse<{ jobList: TaskJob[] }> = await apiCaller.get(
            '/bee/v2/bdaiassistant/job/getList',
            params,
            { silent: true },
        );

        if (res.code === 0 && res.data) {
            return {
                code: res.code,
                msg: res.msg,
                data: res.data,
            };
        }

        return null;
    } catch (error) {
        console.error('获取任务列表失败:', error);
        return null;
    }
};

/**
 * 使用callerRequest获取任务列表的Hook
 * @returns 任务列表请求函数
 */
export const useTaskListApi = () => {
    const callerRequest = useCallerRequest();

    return useMemo(
        () => ({
            /**
             * 获取任务列表 - 使用callerRequest
             * @param taskType 任务类型筛选：'ALL'、'PoiDiagnosis'、'AiCall'，不传则返回全部
             * @param bizId 业务ID
             * @returns 任务列表数据
             */
            getTaskList: async (
                taskType?: 'ALL' | TaskType,
                bizId?: string,
            ): Promise<TaskListResponse | null> => {
                try {
                    const params: any = {};
                    if (taskType) {
                        params.type = taskType;
                    }
                    if (bizId) {
                        params.bizId = bizId;
                    }
                    const res: ApiResponse<{ jobList: TaskJob[] }> =
                        await callerRequest.get(
                            '/bee/v2/bdaiassistant/job/getList',
                            params,
                        );

                    if (res.code === 0 && res.data) {
                        return {
                            code: res.code,
                            msg: res.msg,
                            data: res.data,
                        };
                    }

                    return null;
                } catch (error) {
                    console.error('获取任务列表失败:', error);
                    return null;
                }
            },
        }),
        [callerRequest],
    );
};

/**
 * 记录弹窗行为
 * @returns 记录结果
 */
export const recordPopup = async (): Promise<BFFResponse<any>> => {
    return apiCaller.get(
        '/bee/v2/bdaiassistant/job/recordPopup',
        {},
        { silent: true },
    );
};

/**
 * 清除任务项红点
 * @param id 任务项id
 * @returns 清除结果
 */
export const clearTaskRedDot = async (id: number): Promise<void> => {
    try {
        await apiCaller.post(
            '/bee/v2/bdaiassistant/job/clearRedDot',
            { id },
            { silent: true },
        );
    } catch (error) {
        console.error('清除红点失败:', error);
        // 乐观更新，不抛出错误
    }
};
