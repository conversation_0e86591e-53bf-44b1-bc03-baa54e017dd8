import { StyleSheet, Text, TouchableOpacity, View } from '@mrn/react-native';
import React, { useState } from 'react';
import Svg, { Polygon } from 'react-native-svg';
import Tooltip from 'react-native-walkthrough-tooltip';

import RNImage from '../RNImage';

import NetImages from '@/assets/images/homeRefactor';

interface GuideStep {
    key: string;
    title: string;
    description: string;
    placement?: 'top' | 'bottom' | 'left' | 'right' | 'center';
}

interface GuideManagerProps {
    steps: GuideStep[];
    visible: boolean;
    onClose: (v: number) => void;
    onComplete: () => void;
    onStepChange?: (step: number) => void;
    children: (props: {
        currentStep: number;
        isStepActive: (stepKey: string) => boolean;
        wrapWithTooltip: (
            stepKey: string,
            children: React.ReactNode,
        ) => React.ReactNode;
    }) => React.ReactNode;
}

const GuideManager: React.FC<GuideManagerProps> = ({
    steps,
    visible,
    onClose,
    onComplete,
    onStepChange,
    children,
}) => {
    const [currentStep, setCurrentStep] = useState(0);
    const totalSteps = steps.length;

    const handleNext = () => {
        if (currentStep < totalSteps - 1) {
            const nextStep = currentStep + 1;
            setCurrentStep(nextStep);
            onStepChange?.(nextStep);
        } else {
            onComplete();
            setCurrentStep(null);
        }
    };

    const handleSkip = () => {
        setCurrentStep(null);
        onClose(currentStep);
    };

    const isStepActive = (stepKey: string) => {
        return visible && steps[currentStep]?.key === stepKey;
    };

    // 渲染指向目标的SVG箭头
    const renderArrow = (placement: string) => {
        let arrowStyle;
        let points;

        switch (placement) {
            case 'top':
                // 箭头向下指向目标
                arrowStyle = {
                    position: 'absolute',
                    bottom: -8,
                    alignSelf: 'center',
                    backgroundColor: 'red',
                };
                points = '0,0 16,0 8,8';
                break;
            case 'bottom':
                // 箭头向上指向目标
                arrowStyle = {
                    position: 'absolute',
                    top: -8,
                    alignSelf: 'center',
                };
                points = '0,8 16,8 8,0';
                break;
            case 'left':
                // 箭头向右指向目标
                arrowStyle = {
                    position: 'absolute',
                    right: -8,
                    alignSelf: 'center',
                };
                points = '8,0 8,16 0,8';
                break;
            case 'right':
                // 箭头向左指向目标
                arrowStyle = {
                    position: 'absolute',
                    left: -8,
                    alignSelf: 'center',
                };
                points = '0,0 0,16 8,8';
                break;
            default:
                arrowStyle = {
                    position: 'absolute',
                    top: -8,
                    alignSelf: 'center',
                };
                points = '0,0 16,0 8,8';
        }

        return (
            <View style={arrowStyle}>
                <Svg width="16" height="8" viewBox="0 0 16 8">
                    <Polygon
                        points={points}
                        fill="#FFFFFF"
                        stroke="#E5E5E5"
                        strokeWidth="1"
                    />
                </Svg>
            </View>
        );
    };

    const renderTooltipContent = (step: GuideStep) => (
        <View style={styles.tooltipContent}>
            <View style={styles.guideCard}>
                <RNImage
                    source={{ uri: NetImages.bannerBg }}
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: 300,
                        borderRadius: 12,
                        zIndex: -1,
                    }}
                />
                <Text>
                    <Text style={styles.guideTitle}>{step.title}</Text>
                    <Text style={styles.guideDescription}>
                        {step.description}
                    </Text>
                </Text>

                {/* 底部操作栏：步骤指示器和按钮 */}
                <View style={styles.bottomActionContainer}>
                    <Text style={styles.stepIndicator}>
                        <Text style={{ color: '#222' }}>{currentStep + 1}</Text>
                        /{totalSteps}
                    </Text>

                    <View style={styles.buttonContainer}>
                        <TouchableOpacity
                            style={[
                                styles.skipButton,
                                currentStep === 3 ? { opacity: 0 } : {},
                            ]}
                            onPress={handleSkip}
                        >
                            <Text style={styles.skipButtonText}>跳过</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={styles.nextButton}
                            onPress={handleNext}
                        >
                            <Text style={styles.nextButtonText}>
                                {currentStep < totalSteps - 1
                                    ? '下一步'
                                    : '完成'}
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
            {renderArrow(step.placement || 'bottom')}
        </View>
    );

    const wrapWithTooltip = (
        stepKey: string,
        childrenNode: React.ReactNode,
    ) => {
        const stepIndex = steps.findIndex((step) => step.key === stepKey);
        const step = steps[stepIndex];
        const isActive = isStepActive(stepKey);

        if (!step || !isActive) {
            return childrenNode;
        }

        return (
            <Tooltip
                isVisible={isActive}
                content={renderTooltipContent(step)}
                placement={step.placement || 'bottom'}
                onClose={() => {}} // 禁止点击空白处关闭
                showChildInTooltip={true}
                allowChildInteraction={false}
                backgroundColor="rgba(0, 0, 0, 0.6)"
                contentStyle={styles.tooltipContainer}
            >
                {childrenNode}
            </Tooltip>
        );
    };

    return (
        <>
            {children({
                currentStep,
                isStepActive,
                wrapWithTooltip,
            })}
        </>
    );
};

const styles = StyleSheet.create({
    tooltipContainer: {
        backgroundColor: 'transparent',
        padding: 0,
        borderRadius: 0,
        shadowOpacity: 0,
        elevation: 0,
    },
    tooltipContent: {
        paddingHorizontal: 20,
        paddingVertical: 20,
        alignItems: 'center',
    },
    guideCard: {
        position: 'relative',
        overflow: 'hidden',
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        padding: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 3,
    },
    guideTitle: {
        fontSize: 14,
        fontWeight: 'bold',
        textAlign: 'left',
    },
    guideDescription: {
        fontSize: 14,
        color: '#333333',
        lineHeight: 20,
        textAlign: 'left',
    },
    bottomActionContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: 20,
    },
    stepIndicator: {
        fontSize: 14,
        color: '#999999',
        flex: 1,
    },
    buttonContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    skipButton: {
        backgroundColor: '#EFEDFF',
        borderRadius: 20,
        paddingHorizontal: 20,
        paddingVertical: 12,
    },
    skipButtonText: {
        fontSize: 16,
        color: '#4322FF',
    },
    nextButton: {
        backgroundColor: '#4021FF',
        borderRadius: 20,
        paddingHorizontal: 24,
        paddingVertical: 12,
        marginLeft: 12,
        elevation: 10,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3,
        zIndex: 9999,
    },
    nextButtonText: {
        fontSize: 16,
        color: '#FFFFFF',
        fontWeight: '500',
    },
    arrow: {
        backgroundColor: '#FFFFFF',
    },
});

export default GuideManager;
