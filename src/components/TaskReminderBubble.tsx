import { View, StyleSheet, Text, TouchableOpacity } from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React from 'react';
import Svg, { Path, LinearGradient, Defs, Stop, Rect } from 'react-native-svg';

const styles = StyleSheet.create({
    container: {
        paddingHorizontal: 14,
        paddingVertical: 12,
        flexDirection: 'row',
        alignItems: 'center',
        elevation: 5,
        borderRadius: 12,
        marginRight: 10,
        marginLeft: 10,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
    },
    gradientContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        borderRadius: 12,
        overflow: 'hidden',
    },
    contentContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        zIndex: 1,
        flexShrink: 1,
    },
    icon: {
        width: 18,
        height: 18,
        marginRight: 4,
    },
    iconContainer: {
        width: 10,
        height: 10,
        marginRight: 8,
        borderRadius: 5,
        alignItems: 'center',
        justifyContent: 'center',
        borderColor: '#6047FA',
        borderWidth: 1,
        backgroundColor: 'transparent',
    },
    text: {
        fontSize: 12,
        color: '#999',
    },
    titleText: {
        fontSize: 12,
        color: '#333',
        fontWeight: '500',
        lineHeight: 16,
    },
    contentText: {
        fontSize: 12,
        color: '#888',
        lineHeight: 16,
    },
    arrowContainer: {
        position: 'absolute',
        right: -8,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 12,
    },
    arrowContainerLeft: {
        left: -8,
    },
});

export interface TaskReminderBubbleRenderContentType {
    /** 标题 */
    title?: string;
    /** 内容 */
    content?: string;
    /** 自定义渲染函数（最高优先级） */
    renderContent?: () => React.ReactNode;
    /** 样式配置 */
    styleConfig?: {
        /** 是否展示完成图标 */
        showCompleteIcon?: boolean;
        /** 是否展示箭头 */
        showArrow?: boolean;
    };
    /** 错误处理回调 */
    onRenderError?: (error: Error) => void;
}

export interface TaskReminderBubbleProps {
    /** 气泡位置 */
    messagePosition?: 'left' | 'right';
    onPress?: () => void;
    /** 渲染数据，包含各种自定义选项 */
    renderData?: TaskReminderBubbleRenderContentType;
    /** 箭头指向目标的垂直位置（相对于气泡顶部的距离，单位px） */
    arrowTargetY?: number;
    text?: string;
}

const TaskReminderBubble = (props: TaskReminderBubbleProps) => {
    const { messagePosition = 'right', renderData, arrowTargetY } = props;

    // 默认渲染内容
    const defaultRenderContent = () => {
        // 如果传递了 title 或 content，说明是自定义渲染
        let isCustomRender = renderData?.title || renderData?.content;
        // 兼容历史逻辑，消息内容写死为“商家诊断 任务已全部完成”
        const renderTitle = isCustomRender ? renderData?.title : '商家诊断';
        const renderContent = isCustomRender
            ? renderData?.content
            : '任务已全部完成';
        const renderStyleConfig = {
            showArrow: renderData?.styleConfig?.showArrow ?? true,
            showCompleteIcon: renderData?.styleConfig?.showCompleteIcon ?? true,
        };

        return (
            <View style={[styles.contentContainer]}>
                {renderStyleConfig?.showCompleteIcon ? (
                    <View style={styles.iconContainer}>
                        <Icon type="check" size={7} tintColor="#6047FA" />
                    </View>
                ) : null}
                <Text
                    style={[
                        styles.text,
                        {
                            flexWrap: 'wrap',
                            flexShrink: 1,
                            marginRight: 8,
                        },
                    ]}
                    numberOfLines={0}
                >
                    {renderTitle ? (
                        <Text style={[styles.titleText]}>
                            {renderTitle + ' '}
                        </Text>
                    ) : null}
                    {renderContent ? (
                        <Text style={[styles.contentText]}>
                            {renderContent}
                        </Text>
                    ) : null}
                </Text>
                {renderStyleConfig?.showArrow ? (
                    <Icon type="right" size={10} tintColor="#666" />
                ) : null}
            </View>
        );
    };

    // 安全渲染函数包装器
    const safeRender = (renderFn: () => React.ReactNode): React.ReactNode => {
        try {
            return renderFn();
        } catch (error) {
            // 调用错误处理回调
            renderData?.onRenderError?.(error as Error);
            // 降级到默认渲染
            return defaultRenderContent();
        }
    };

    // 渲染内容的逻辑
    const renderContent = (): React.ReactNode => {
        // 最高优先级：自定义渲染函数
        if (renderData?.renderContent) {
            return safeRender(() => renderData.renderContent!());
        }
        return defaultRenderContent();
    };

    const handlePress = () => {
        props.onPress?.();
    };

    return (
        <TouchableOpacity
            onPress={handlePress}
            activeOpacity={0.8}
            style={[
                styles.container,
                {
                    maxWidth: 240,
                    position: 'relative',
                    alignSelf: 'flex-start',
                },
            ]}
        >
            {/* 渐变背景 */}
            <View style={styles.gradientContainer}>
                <Svg
                    width="100%"
                    height="100%"
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                    }}
                    preserveAspectRatio="none"
                    viewBox="0 0 100 100"
                >
                    <Defs>
                        <LinearGradient
                            id="bubbleGradient"
                            x1={messagePosition === 'right' ? '0%' : '100%'}
                            y1="0%"
                            x2={messagePosition === 'right' ? '100%' : '0%'}
                            y2="0%"
                        >
                            <Stop offset="0%" stopColor="#E8F4FD" />
                            <Stop offset="100%" stopColor="#F8FCFF" />
                        </LinearGradient>
                    </Defs>
                    <Rect
                        x="0"
                        y="0"
                        width="100"
                        height="100"
                        fill="url(#bubbleGradient)"
                    />
                </Svg>
            </View>

            {messagePosition === 'left' ? (
                <View
                    style={[
                        styles.arrowContainer,
                        styles.arrowContainerLeft,
                        {
                            top:
                                arrowTargetY !== undefined
                                    ? arrowTargetY - 8
                                    : '50%',
                            marginTop: arrowTargetY !== undefined ? 0 : -8,
                        },
                    ]}
                >
                    <Svg width={8} height={16} viewBox="0 0 8 16">
                        <Path d="M8 0 Q2 4 0 8 Q2 12 8 16 Z" fill={'#F8FCFF'} />
                    </Svg>
                </View>
            ) : null}
            {renderContent()}
            {/* SVG 气泡箭头 - 指向右侧 */}
            {messagePosition === 'right' ? (
                <View
                    style={[
                        styles.arrowContainer,
                        {
                            top:
                                arrowTargetY !== undefined
                                    ? arrowTargetY - 8
                                    : '50%',
                            marginTop: arrowTargetY !== undefined ? 0 : -8,
                        },
                    ]}
                >
                    <Svg width={8} height={16} viewBox="0 0 8 16">
                        {/* 箭头主体 */}
                        <Path d="M0 0 Q6 4 8 8 Q6 12 0 16 Z" fill={'#F8FCFF'} />
                    </Svg>
                </View>
            ) : null}
        </TouchableOpacity>
    );
};

export default TaskReminderBubble;
