import { deviceEventEmitter } from '@mrn/mrn-utils';
import { useInterval } from 'ahooks';
import { useCallback, useEffect, useRef, useState } from 'react';
import { DeviceEventEmitter } from 'react-native';

import { getTaskStatus, recordPopup } from '../../../api/taskApi';
import { SOURCE } from '../../../types';

import { TaskReminderBubbleRenderContentType } from '@/components/TaskReminderBubble';

const sourceToTab = {
    [SOURCE.home]: 'Base',
    [SOURCE.wdcAdopt]: 'PoiCenter',
    [SOURCE.wdcNearby]: 'PoiCenter',
    [SOURCE.wdcAll]: 'PoiCenter',
    [SOURCE.wdcResponsible]: 'PoiCenter',
    [SOURCE.wdcConcerned]: 'PoiCenter',
    [SOURCE.dove]: 'Homer',
    [SOURCE.tabWorkbench]: 'Workbench',
    [SOURCE.tabMine]: 'Mine',
};

const useNotify = (
    visible: boolean,
    source: SOURCE,
    autoHideTaskReminderDelay: number,
    onShowBubble?: () => void,
    onHideBubble?: () => void,
) => {
    const [isShowTaskReminder, setIsShowTaskReminder] = useState(false);
    const [renderData, setRenderData] = useState<any>(null);
    const bubbleTimer = useRef(null); // 气泡自动隐藏定时器
    const pollingTimer = useRef(null); // 轮询定时器
    const isPollingPaused = useRef(false); // 轮询是否暂停

    // 队列管理状态
    const notifyQueue = useRef<string[]>([]); // 提醒队列
    const queueTimer = useRef<NodeJS.Timeout | null>(null); // 队列处理定时器

    // 添加到提醒队列
    const addToNotifyQueue = useCallback((jobNames: string[]) => {
        if (jobNames && jobNames.length > 0) {
            notifyQueue.current = [...notifyQueue.current, ...jobNames];
        }
    }, []);

    // 处理提醒队列
    const processNotifyQueue = useCallback(() => {
        if (notifyQueue.current.length === 0) {
            return;
        }

        const nextNotify = notifyQueue.current.shift();

        if (nextNotify) {
            showTaskReminder({ title: nextNotify, content: '任务已全部完成' });
        }
    }, []);

    // 清理队列定时器
    const clearQueueTimer = useCallback(() => {
        if (queueTimer.current) {
            clearTimeout(queueTimer.current);
            queueTimer.current = null;
        }
    }, []);

    // 隐藏TaskReminderBubble并发起recordPopup请求
    const hideTaskReminder = useCallback(async () => {
        if (isShowTaskReminder) {
            try {
                await recordPopup();
            } catch (error) {
                console.error(
                    'BEE_ASSISTANT_DEBUG',
                    'Failed to record popup:',
                    error,
                );
            }
            setIsShowTaskReminder(false);
            setRenderData(null); // 清除渲染数据
            // 通知父组件消息气泡隐藏，用于决定是否重新折叠icon
            onHideBubble?.();
            setIsShowTaskReminder(false);
        }
    }, [isShowTaskReminder, clearQueueTimer, onHideBubble]);

    // 手动显示消息气泡，支持多种参数形式
    const showTaskReminder = useCallback(
        (options?: TaskReminderBubbleRenderContentType) => {
            setRenderData(options);
            // 通知父组件消息气泡显示，用于自动展开折叠的icon
            onShowBubble?.();
            setIsShowTaskReminder(true);
        },
        [onShowBubble],
    );

    const fetchRunningJob = useCallback(async () => {
        try {
            const data = await getTaskStatus();

            // 优先处理 notifyJobNames 队列
            if (data?.notifyJobNames && data.notifyJobNames.length > 0) {
                addToNotifyQueue(data.notifyJobNames);
            }
        } catch (error) {
            console.error('Failed to fetch running job:', error);
        }
    }, [addToNotifyQueue, onShowBubble]);

    // 开始轮询
    const startPolling = useCallback(() => {
        if (!visible) {
            return;
        }
        if (pollingTimer.current) {
            clearInterval(pollingTimer.current);
        }
        pollingTimer.current = setInterval(() => {
            fetchRunningJob();
        }, 10000);
        isPollingPaused.current = false;
    }, [fetchRunningJob, visible]);

    // 暂停轮询
    const pausePolling = useCallback(() => {
        if (pollingTimer.current) {
            clearInterval(pollingTimer.current);
            pollingTimer.current = null;
        }
        isPollingPaused.current = true;
    }, []);

    // 轮询 fetchRunningJob
    useEffect(() => {
        fetchRunningJob(); // 初始调用
        startPolling(); // 开始轮询

        return () => {
            if (pollingTimer.current) {
                clearInterval(pollingTimer.current);
            }
        };
    }, [fetchRunningJob, startPolling]);

    // 气泡展示5s后自动隐藏
    useEffect(() => {
        if (isShowTaskReminder) {
            // 清除之前的定时器
            if (bubbleTimer.current) {
                clearTimeout(bubbleTimer.current);
            }

            // 自动隐藏
            bubbleTimer.current = setTimeout(() => {
                hideTaskReminder();
            }, autoHideTaskReminderDelay);
        }

        return () => {
            if (bubbleTimer.current) {
                clearTimeout(bubbleTimer.current);
            }
        };
    }, [isShowTaskReminder, hideTaskReminder]);

    // 从首页直接切换到其他bundle的场景
    useEffect(() => {
        if (!visible) {
            return;
        }
        const sub1 = deviceEventEmitter.addListener(
            'containerViewDidAppear',
            () => {
                // 如果有暂停的计时则开始计时
                if (
                    isPollingPaused.current &&
                    currentTab === sourceToTab[source]
                ) {
                    startPolling();
                }
            },
        );
        const sub2 = deviceEventEmitter.addListener(
            'containerViewDidDisappear',
            () => {
                // 如果有正在进行的计时则暂停计时
                if (pollingTimer.current) {
                    pausePolling();
                }
            },
        );
        return () => {
            sub1.remove();
            sub2.remove();
        };
    }, [startPolling, pausePolling, visible]);

    const [currentTab, setCurrentTab] = useState('Base');

    useEffect(() => {
        const sub = DeviceEventEmitter.addListener(
            'BEE_BASE_TAB_CHANGE',
            (tab) => {
                setCurrentTab(tab);
            },
        );
        return () => {
            sub.remove();
        };
    }, []);

    useEffect(() => {
        if (currentTab === sourceToTab[source]) {
            startPolling();
        } else {
            pausePolling();
        }
    }, [currentTab]);

    useInterval(() => {
        if (isShowTaskReminder) {
            return;
        }
        processNotifyQueue();
    }, 2000);

    return {
        isShowTaskReminder,
        hideTaskReminder,
        showTaskReminder,
        renderData,
    };
};

export default useNotify;
