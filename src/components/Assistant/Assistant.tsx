import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import {
    Animated,
    LayoutAnimation,
    PanResponder,
    Platform,
    StyleSheet,
    TouchableOpacity,
    View,
    Dimensions,
    StatusBar,
} from '@mrn/react-native';
import { useDebounceFn, useGetState } from 'ahooks';
import _ from 'lodash';
import React, {
    forwardRef,
    useEffect,
    useImperativeHandle,
    useRef,
    useState,
    useCallback,
} from 'react';

import useNotify from './hooks/useNotify';
import useAssistantClose from '../../hooks/useAssistantClose';
import useGrayInfo from '../../hooks/useGrayInfo';
import { useLayout } from '../../hooks/useLayout';
import { useUser } from '../../store/user';
import { SOURCE } from '../../types';
import { startAnimation } from '../../utils/animation';
import openChat from '../../utils/openChat';
import { track, trackEvent, TrackEventType } from '../../utils/track';
import AssistantIcon from '../AssistantIcon';
import TaskReminderBubble, {
    TaskReminderBubbleRenderContentType,
} from '../TaskReminderBubble';

// 获取屏幕尺寸，用于计算图标拖拽边界
const { width: screenWidth, height: screenHeight } = Dimensions.get('screen');

/**
 * Assistant 组件的属性接口
 * 用于配置悬浮助手图标的行为和定位
 */
export interface AssistantProps {
    /** 滚动动画值，用于监听页面滚动以触发折叠行为 */
    scrollY?: Animated.Value;
    /** 滚动配置，生效的前提是 scrollY 有值 */
    scrollYConfig?: {
        /** 滚动停止后是否自动展开图标，默认为 true */
        autoExpandEnabled?: boolean;
        /** 滚动停止后自动展开的延迟时间（毫秒），默认为 2000ms 后自动再次展开 */
        expandDelay?: number;
        /** 滚动停止后自动展开的回调函数 */
        expandCallback?: () => void;
    };
    /** 页面来源标识，决定图标在不同页面的行为差异 */
    source?: SOURCE | string;
    /** 顶部偏移量，用于适配有顶部导航栏的页面（如公私海容器顶部的tab， 或者其他页面有负边距） */
    baseTop?: number;
    /** 图标的默认位置配置 */
    defaultPosition?: {
        top?: number;
        left?: number;
        right?: number;
        bottom?: number;
    };
    /** 自动隐藏任务气泡的时延, 默认 5000ms */
    autoHideTaskReminderDelay?: number;
}

/**
 * Assistant 组件暴露给父组件的方法接口
 * 通过 ref 调用这些方法来控制助手图标的行为
 */
export interface AssistantRef {
    /** 折叠图标到屏幕边缘 */
    foldIcon: (
        draftPosition?: Partial<{
            top: number;
            left: number;
            right: number;
            bottom: number;
        }>,
    ) => void;
    /** 展开图标 */
    unfoldIcon: () => void;
    /** 隐藏任务提醒气泡 */
    hideTaskReminder: () => void;
    /** 手动触发，显示任务提醒气泡 */
    showTaskReminder: (options?: TaskReminderBubbleRenderContentType) => void;
    /** 获取当前图标位置 */
    getPosition: () => Partial<{
        top: number;
        left: number;
        right: number;
        bottom: number;
    }>;
    /** 设置图标到指定位置 */
    setPosition: (
        position: Partial<{
            top: number;
            left: number;
            right: number;
            bottom: number;
        }>,
    ) => void;
}

const styles = StyleSheet.create({
    container: {
        position: 'absolute',
        zIndex: 1000,
        elevation: 1000,
    },
    icon: {
        ...Platform.select({
            ios: {
                shadowColor: '#171717',
                shadowOffset: {
                    width: 0,
                    height: 2,
                },
                shadowOpacity: 0.4,
                shadowRadius: 8,
            },
            android: {
                borderRadius: 55,
                backgroundColor: '#fff',
            },
        }),
    },
});

const ICON_SIZE = 55; // 图标尺寸
const HORIZONTAL_PADDING = 10; // 水平边距

const Assistant = forwardRef<AssistantRef, AssistantProps>((props, ref) => {
    // 页面来源处理，兜底为首页
    const source = (props.source || SOURCE.home) as SOURCE;

    const { bottom } = useSafeAreaInsets();
    const bottomTabHeight = Platform.select({
        ios: bottom + 49,
        android: bottom + 49,
    });
    const isPoiPage = [
        SOURCE.wdcAdopt,
        SOURCE.wdcNearby,
        SOURCE.wdcAll,
        SOURCE.wdcResponsible,
        SOURCE.wdcConcerned,
    ].includes(source);

    // 这些页面有底部 TAb
    const isMainPage = [
        SOURCE.wdcAdopt,
        SOURCE.wdcNearby,
        SOURCE.wdcAll,
        SOURCE.wdcResponsible,
        SOURCE.wdcConcerned,
        SOURCE.home,
        SOURCE.tabMine,
        SOURCE.tabWorkbench,
    ].includes(source);
    const { top } = useSafeAreaInsets();
    const statusBarHeight = Platform.select({
        ios: top,
        android: StatusBar.currentHeight,
    });

    // 计算顶部基准位置（考虑页面顶部导航的高度）
    const baseTop = props.baseTop || (isPoiPage ? statusBarHeight + 44 : 0); // 44为商家页tab高度

    const VERTICAL_PADDING = 20;

    // 图标可拖拽的边界范围
    const bounds = {
        // 底部边界：主页面需要考虑底部Tab高度
        bottom: isMainPage
            ? screenHeight - (bottomTabHeight + ICON_SIZE + VERTICAL_PADDING)
            : screenHeight - (ICON_SIZE + VERTICAL_PADDING),
        // 顶部边界：基准位置加上垂直边距
        top: baseTop + VERTICAL_PADDING,
    };

    const { user } = useUser();
    const timer = useRef(null);
    const opacity = useRef(new Animated.Value(1)).current;
    const { layout, onLayout } = useLayout({ delay: 0 });
    const [visible, setVisible] = useState(false);

    const animationPlaying = useRef(false);
    const grayInfo = useGrayInfo();

    const [moving, setMoving] = useState(false); // 是否正在拖拽
    const [isFolded, setIsFolded] = useState(false); // 是否处于折叠状态
    const hasFolderByUserDrag = useRef(false); // 记录小蜜 icon 是否被用户强制隐藏，只有 强制拖出/点击折叠 icon 才可以取消这个状态

    /**
     * 助手图标点击处理函数（防抖处理）
     * 折叠状态下点击先展开图标，正常状态下打开聊天界面
     */
    const handleAssistantIconPress = useDebounceFn(
        () => {
            // 如果icon处于折叠状态，先展开
            if (isFolded) {
                // 手动点击 icon，触发展开
                hasFolderByUserDrag.current = false;
                unfoldIcon();
                setIsFolded(false);
                return;
            }

            // 埋点统计用户点击行为
            trackEvent('icon_click', { misId: user.misId }, TrackEventType.MC, {
                cid: 'c_waimai_e_bee_rn_assistant_icon',
            });

            // 打开聊天界面
            openChat(source);
            // 隐藏任务提醒气泡
            hideTaskReminder();
            return;
        },
        { wait: 200 }, // 200ms防抖延迟
    );

    /**
     * 任务提醒气泡点击处理函数
     * 直接打开聊天界面并展开任务抽屉
     */
    const handleTaskReminderPress = useDebounceFn(
        () => {
            openChat(source, { openTaskDrawer: true }); // 打开聊天并展开任务抽屉
            hideTaskReminder(); // 隐藏提醒气泡
        },
        { wait: 200 },
    );

    /**
     * 折叠图标函数
     * 将图标部分隐藏到屏幕边缘，并降低透明度
     * @param draftPosition 可选的位置参数
     * @param source 折叠触发源
     */
    const foldIcon = (draftPosition?) => {
        if (isFolded) {
            return;
        }
        // 如果动画正在播放，避免重复执行打断动画
        if (animationPlaying.current) {
            return;
        }
        animationPlaying.current = true;

        const position = draftPosition || getPosition();
        // 根据当前位置决定向哪边折叠
        if (position.right) {
            setPosition({
                ...position,
                right: HORIZONTAL_PADDING - 45, // 向右边缘折叠
            });
        } else {
            setPosition({
                ...position,
                left: HORIZONTAL_PADDING - 45, // 向左边缘折叠
            });
        }

        startAnimation(); // 启动位置动画

        // 200ms后重置动画状态
        setTimeout(() => {
            animationPlaying.current = false;
        }, 200);

        // 降低透明度到50%
        Animated.spring(opacity, {
            toValue: 0.5,
            useNativeDriver: true,
        }).start();

        setIsFolded(true);
    };

    /**
     * 展开图标函数
     * 将图标完全显示，恢复正常透明度
     */
    const unfoldIcon = () => {
        const position = getPosition();
        // 将图标位置恢复到正常的边距位置
        setPosition({
            ...position,
            right: position.right ? HORIZONTAL_PADDING : undefined,
            left: position.left ? HORIZONTAL_PADDING : undefined,
        });

        startAnimation(); // 启动位置动画

        Animated.spring(opacity, {
            toValue: 1,
            useNativeDriver: true,
        }).start();

        setIsFolded(false);
    };

    // 消息气泡显示时的回调，用于自动展开折叠的icon
    const handleShowBubble = useCallback(() => {
        if (isFolded) {
            unfoldIcon();
        }
    }, [isFolded]);

    // 消息气泡隐藏时的回调，用于根据折叠原因决定是否重新折叠
    const handleHideBubble = useCallback(() => {
        // 如果之前是被用户主动拖拽折叠，则需要再次自动折叠
        if (hasFolderByUserDrag.current) {
            foldIcon();
        }
    }, []);

    const {
        isShowTaskReminder,
        hideTaskReminder,
        showTaskReminder,
        renderData,
    } = useNotify(
        visible,
        source,
        props.autoHideTaskReminderDelay || 5000,
        handleShowBubble,
        handleHideBubble,
    );

    useEffect(() => {
        setVisible(grayInfo?.gray);
    }, [grayInfo]);

    /**
     * 滚动监听效果
     * 滚动时自动折叠图标，根据配置决定是否自动展开以及展开时机
     */
    useEffect(() => {
        if (!props.scrollY) {
            return;
        }

        // 解构配置参数，提供默认值
        const {
            autoExpandEnabled = true,
            expandDelay = 2000,
            expandCallback,
        } = props?.scrollYConfig || {};

        props.scrollY.addListener(() => {
            // 如果在滚动的过程中正好有消息气泡显示，则不折叠
            if (isShowTaskReminder) {
                return;
            }
            foldIcon(); // 滚动时立即折叠

            clearTimeout(timer.current);

            // 如果启用自动展开且并没有被用户手动收起，则在指定延迟后展开图标
            if (autoExpandEnabled && !hasFolderByUserDrag.current) {
                timer.current = setTimeout(() => {
                    unfoldIcon();
                    // 调用展开回调函数（如果提供）
                    expandCallback?.();
                }, expandDelay);
            }
        });

        // 清理函数
        return () => {
            clearTimeout(timer.current);
            props.scrollY.removeAllListeners();
        };
    }, [props.scrollY, props.scrollYConfig, isShowTaskReminder]);

    useEffect(() => {
        if (!visible) {
            return;
        }

        track('icon');
    }, [visible]);

    // 监听助手关闭事件
    useAssistantClose(source);

    // 默认位置处理：确保左右边距符合设计规范
    const processedDefaultPosition = props.defaultPosition
        ? {
              ...props.defaultPosition,
              ...(props.defaultPosition.left && { left: HORIZONTAL_PADDING }),
              ...(props.defaultPosition.right && { right: HORIZONTAL_PADDING }),
          }
        : undefined;
    /**
     * 图标位置状态管理
     * 使用useGetState确保能在异步操作中获取最新的位置状态
     */
    const [position, setPosition, getPosition] = useGetState<
        Partial<{
            top: number;
            left: number;
            right: number;
            bottom: number;
        }>
    >(
        // 默认位置：右下角
        processedDefaultPosition || {
            right: HORIZONTAL_PADDING,
            bottom: VERTICAL_PADDING,
        },
    );

    // 暴露方法给父组件
    useImperativeHandle(
        ref,
        () => ({
            foldIcon,
            unfoldIcon,
            hideTaskReminder,
            showTaskReminder,
            getPosition,
            setPosition,
        }),
        [
            foldIcon,
            unfoldIcon,
            hideTaskReminder,
            showTaskReminder,
            getPosition,
            setPosition,
        ],
    );

    /**
     * 拖拽手势响应器
     * 处理图标的拖拽移动、自动吸附和折叠逻辑
     */
    const panResponder = useRef(
        PanResponder.create({
            onStartShouldSetPanResponder: () => {
                return true;
            },
            onMoveShouldSetPanResponder: (_, gestureState) => {
                const { dx, dy } = gestureState;
                // 只有当移动距离足够大时才启动拖动，避免误触
                if (Math.abs(dx) > 10 || Math.abs(dy) > 10) {
                    setMoving(true);
                    return true;
                }
                return false;
            },
            onPanResponderMove: (_, gestureState) => {
                const { dx, dy } = gestureState;
                // 忽略微小的移动
                if (Math.abs(dx) < 10 && Math.abs(dy) < 10) {
                    return;
                }

                // 拖拽时保持完全不透明
                opacity.setValue(1);
                // 更新位置：跟随手指移动
                setPosition({
                    top: gestureState.moveY - baseTop, // 考虑顶部偏移
                    left: gestureState.moveX,
                });
                startAnimation();
            },
            // 拖拽结束处理
            onPanResponderRelease: (_, gestureState) => {
                setMoving(false);
                const position = getPosition();
                const { dx, dy } = gestureState;

                // 如果移动距离很小，不处理位置变化，让子组件处理点击事件
                if (Math.abs(dx) < 10 && Math.abs(dy) < 10) {
                    return;
                }

                let draftPosition = { ...position };

                /**
                 * 自动吸附到屏幕边缘的逻辑
                 * 根据最终位置决定吸附到左边还是右边
                 */
                const alignSide = () => {
                    if (gestureState.moveX > screenWidth / 2) {
                        // 超过屏幕中线，吸附到右边
                        draftPosition = {
                            right: HORIZONTAL_PADDING,
                            top: position.top,
                        };
                    } else {
                        // 未超过屏幕中线，吸附到左边
                        draftPosition = {
                            left: HORIZONTAL_PADDING,
                            top: position.top,
                        };
                    }
                };
                // 如果存在scrollY，使用原有的拖拽逻辑
                alignSide();

                /**
                 * 判断是否需要折叠图标
                 * 条件：没有滚动监听 && 主要是水平移动 && 移动到屏幕边缘
                 */
                if (
                    Math.abs(gestureState.dx) + 20 >
                        Math.abs(gestureState.dy) &&
                    Math.min(
                        gestureState.moveX,
                        screenWidth - gestureState.moveX,
                    ) < 10
                ) {
                    hasFolderByUserDrag.current = true;
                    foldIcon(draftPosition);
                } else {
                    hasFolderByUserDrag.current = false;
                    setPosition(draftPosition);
                    startAnimation(LayoutAnimation.Presets.spring);
                }
            },
        }),
    ).current;

    /**
     * 边界检测和位置修正
     * 确保图标始终在可见区域内，超出边界时自动调整
     */
    useEffect(() => {
        // 正在拖拽或布局信息未准备好时不执行
        if (moving || layout.pageY === null) {
            return;
        }

        // 处理水平边界：图标超出屏幕边缘时降低透明度
        if (position.right < 0 || position.left < 0) {
            Animated.spring(opacity, {
                toValue: 0.5, // 半透明状态
                useNativeDriver: true,
            }).start();
        } else {
            Animated.spring(opacity, {
                toValue: 1, // 完全不透明
                useNativeDriver: true,
            }).start();
        }

        // 处理垂直边界：超出上边界时调整到顶部
        if (layout.pageY < bounds.top) {
            setPosition({
                ..._.pick(position, ['right', 'left']), // 保持水平位置
                top: bounds.top,
            });
            startAnimation(LayoutAnimation.Presets.spring);
        }

        // 处理垂直边界：超出下边界时调整到底部
        if (layout.pageY > bounds.bottom) {
            setPosition({
                ..._.pick(position, ['right', 'left']), // 保持水平位置
                bottom: VERTICAL_PADDING,
            });
            startAnimation(LayoutAnimation.Presets.spring);
        }
    }, [layout]);

    // 屏蔽私海页
    if (
        [SOURCE.wdcAll, SOURCE.wdcResponsible, SOURCE.tabWorkbench].includes(
            source,
        )
    ) {
        return null;
    }

    // 暂时屏蔽
    if (!visible) {
        return null;
    }

    return (
        <View
            style={[styles.container, position]}
            onLayout={onLayout}
            onTouchStart={() => {
                // 触摸开始时延迟隐藏任务提醒气泡
                setTimeout(() => {
                    hideTaskReminder();
                }, 200);
            }}
            {...panResponder.panHandlers}
        >
            {/* 引导对齐有问题，在安卓下纵向有偏移，不知道原因 */}
            {visible ? (
                <Animated.View
                    style={{
                        // 根据小蜜的位置决定消息气泡的位置
                        flexDirection: position.right ? 'row' : 'row-reverse',
                        opacity: opacity,
                        alignItems: 'flex-start',
                    }}
                >
                    {isShowTaskReminder && (
                        <View style={{ marginTop: 10 }}>
                            <TaskReminderBubble
                                messagePosition={
                                    position?.right ? 'right' : 'left'
                                }
                                onPress={handleTaskReminderPress.run}
                                renderData={renderData}
                                arrowTargetY={ICON_SIZE / 2 - 10} // 图标中心位置减去气泡的marginTop
                            />
                        </View>
                    )}
                    <TouchableOpacity
                        style={styles.icon}
                        onPress={handleAssistantIconPress.run}
                    >
                        <AssistantIcon size={ICON_SIZE} />
                    </TouchableOpacity>
                </Animated.View>
            ) : null}
        </View>
    );
});

export default Assistant;
