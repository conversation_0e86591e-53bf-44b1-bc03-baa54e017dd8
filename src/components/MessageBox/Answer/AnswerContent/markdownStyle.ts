import { Platform, StyleSheet } from 'react-native';

import { COLORS, MessageContainerWidth } from '@/consts';

// this is converted to a stylesheet internally at run time with StyleSheet.create(
export const markdownStyles = {
    // Headings
    heading1: {
        flexDirection: 'row',
        fontSize: 32,
        marginVertical: 6,
        fontWeight: 'bold',
    },
    heading2: {
        flexDirection: 'row',
        fontSize: 24,
        marginVertical: 6,
        fontWeight: 'bold',
    },
    heading3: {
        flexDirection: 'row',
        fontSize: 18,
        marginVertical: 6,
        fontWeight: 'bold',
    },
    heading4: {
        flexDirection: 'row',
        fontSize: 16,
        marginVertical: 6,
        fontWeight: 'bold',
    },
    heading5: {
        flexDirection: 'row',
        fontSize: 13,
        marginVertical: 6,
        fontWeight: 'bold',
    },
    heading6: {
        flexDirection: 'row',
        fontSize: 11,
        marginVertical: 6,
        fontWeight: 'bold',
    },

    // Horizontal Rule
    hr: {
        marginVertical: 12,
        backgroundColor: '#666',
        height: StyleSheet.hairlineWidth,
        marginLeft: -16,
        width: MessageContainerWidth,
    },

    // Emphasis
    strong: {
        fontWeight: 'bold',
    },
    em: {
        fontStyle: 'italic',
    },
    s: {
        textDecorationLine: 'line-through',
    },

    // Blockquotes
    blockquote: {
        backgroundColor: '#F5F5F5',
        borderColor: '#CCC',
        borderLeftWidth: 4,
        marginLeft: 5,
        paddingHorizontal: 5,
    },

    // Lists
    bullet_list: {
        lineHeight: 21,
    },
    ordered_list: {
        lineHeight: 21,
    },
    list_item: {
        flexDirection: 'row',
        justifyContent: 'flex-start',
        marginVertical: 2,
    },
    // @pseudo class, does not have a unique render rule
    bullet_list_icon: {
        marginLeft: 4,
        marginRight: 4,
    },
    // @pseudo class, does not have a unique render rule
    bullet_list_content: {
        flex: 1,
    },
    // @pseudo class, does not have a unique render rule
    ordered_list_icon: {
        marginLeft: 10,
        marginRight: 10,
    },
    // @pseudo class, does not have a unique render rule
    ordered_list_content: {
        flex: 1,
    },

    // Code
    code_inline: {
        borderWidth: 1,
        borderColor: '#CCCCCC',
        backgroundColor: '#f5f5f5',
        padding: 10,
        borderRadius: 4,
        ...Platform.select({
            ['ios']: {
                fontFamily: 'Courier',
            },
            ['android']: {
                fontFamily: 'monospace',
            },
        }),
    },
    code_block: {
        marginVertical: 12,
        borderWidth: 1,
        borderColor: '#CCCCCC',
        backgroundColor: '#f5f5f5',
        padding: 10,
        borderRadius: 4,
        ...Platform.select({
            ['ios']: {
                fontFamily: 'Courier',
            },
            ['android']: {
                fontFamily: 'monospace',
            },
        }),
    },
    fence: {
        borderWidth: 1,
        borderColor: '#CCCCCC',
        backgroundColor: '#f5f5f5',
        padding: 10,
        borderRadius: 4,
        ...Platform.select({
            ['ios']: {
                fontFamily: 'Courier',
            },
            ['android']: {
                fontFamily: 'monospace',
            },
        }),
    },

    // Tables
    table: {
        borderWidth: StyleSheet.hairlineWidth,
        lineHeight: 21,
        borderColor: '#eee',
        borderRadius: 8,
    },
    thead: {
        backgroundColor: '#F5F6FA',
        borderTopLeftRadius: 8,
        borderTopRightRadius: 8,
    },
    tbody: {},
    th: {
        flex: 1,
        padding: 8,
        borderRightWidth: StyleSheet.hairlineWidth,
        borderColor: '#eee',
        width: 100,
        fontWeight: 'bold',
        color: '#999',
    },
    tr: {
        flexDirection: 'row',
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderColor: '#eee',
    },
    td: {
        flex: 1,
        padding: 8,
        borderRightWidth: StyleSheet.hairlineWidth,
        borderColor: '#eee',
        width: 100,
    },

    // Links
    link: {
        textDecorationLine: undefined,
        color: COLORS.LINK,
        backgroundColor: '#666',
    },
    blocklink: {
        flex: 1,
        borderColor: '#000000',
        borderBottomWidth: 1,
    },

    // Images
    image: {
        flex: 1,
    },

    // Text Output
    text: {},
    textgroup: {
        color: '#222',
    },
    paragraph: {
        marginTop: 10,
        marginBottom: 10,
        flexWrap: 'wrap',
        flexDirection: 'row',
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
        width: '100%',
        lineHeight: 21,
    },
    hardbreak: {
        width: '100%',
        height: 1,
    },
    softbreak: {},

    // Believe these are never used but retained for completeness
    pre: {},
    inline: {},
    span: {},
};
