import { Text } from '@mrn/react-native';
import React, { useMemo } from 'react';
import Markdown from 'react-native-markdown-display';

import { markdownStyles } from './markdownStyle';
import useOpenLink from '../../../../utils/openLink';

import { COLORS } from '@/consts';

export const MemoizedMarkdownInner = React.memo(
    ({
        children,
        onLinkPress,
        style,
    }: {
        children: string;
        onLinkPress?: (url: string) => void;
        style?: any;
    }) => {
        const openLink = useOpenLink();

        const rules = useMemo(
            () => ({
                text: (
                    node,
                    children,
                    parent,
                    styles,
                    inheritedStyles = {},
                ) => (
                    <Text key={node.key} style={[inheritedStyles, styles.text]}>
                        {node.content.split('<br>').join('\n')}
                    </Text>
                ),
                html_inline: () => {
                    return <Text>{'\n'}</Text>;
                },
                link: (data) => {
                    const { attributes, children } = data;
                    return (
                        <Text
                            style={[style?.link, { color: COLORS.LINK }]}
                            onPress={() => {
                                if (typeof onLinkPress === 'function') {
                                    return onLinkPress(attributes.href);
                                }
                                openLink(attributes.href);
                            }}
                        >
                            {children[0].content}
                        </Text>
                    );
                },
                image: () => {
                    return null;
                },
            }),
            [openLink],
        );

        return (
            <Markdown
                key={children}
                style={style || (markdownStyles as any)}
                rules={rules}
            >
                {children}
            </Markdown>
        );
    },
    (prev, next) => prev.children === next.children,
);
