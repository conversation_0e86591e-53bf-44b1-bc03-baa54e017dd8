import { ActivityIndicator, TouchableOpacity, View } from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React, { useState } from 'react';

import Condition from '@/components/Condition/Condition';
import RNText from '@/components/RNText';
import { COLORS } from '@/consts';
import TWS from '@/TWS';
import { ThinkContentMessage } from '@/types/message';

const ThinkContent = ({
    children,
    status,
}: {
    children: ThinkContentMessage[];
    status: string;
}) => {
    const data = children.map((v) => v.insert.thinkContent);

    const [expanded, setExpanded] = useState(true);

    if (!data?.length) {
        return null;
    }

    return (
        <View>
            <View style={[TWS.row()]}>
                <Condition
                    condition={[status === 'thinking', status === 'done']}
                >
                    <>
                        <ActivityIndicator
                            size={16}
                            color={COLORS.PRIMARY}
                            style={{ marginRight: 4 }}
                        />
                        <RNText
                            style={{
                                fontSize: 12,
                                color: '#666',
                                fontWeight: '500',
                            }}
                        >
                            思考中...
                        </RNText>
                    </>
                    <>
                        <Icon
                            type="success-o"
                            tintColor={'#FF833D'}
                            size={16}
                        />
                        <RNText
                            style={{
                                fontSize: 12,
                                color: '#666',
                                fontWeight: '500',
                            }}
                        >
                            思考完成
                        </RNText>
                    </>
                </Condition>
                <Condition condition={[status === 'done']}>
                    <TouchableOpacity onPress={() => setExpanded(!expanded)}>
                        <Icon
                            type={expanded ? 'expand-less' : 'expand-more'}
                            tintColor={'#666'}
                            size={16}
                        />
                    </TouchableOpacity>
                </Condition>
            </View>
            <Condition condition={[expanded]}>
                <View
                    style={{
                        borderLeftColor: '#eee',
                        borderLeftWidth: 1,
                        paddingLeft: 8,
                        marginLeft: 8,
                        marginTop: 2,
                    }}
                >
                    <RNText
                        style={{ fontSize: 12, color: '#666', lineHeight: 18 }}
                    >
                        {data.map((v) => v.content).join('')}
                    </RNText>
                </View>
            </Condition>
        </View>
    );
};

export default ThinkContent;
