import { openPage } from '@mfe/bee-foundation-utils';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Image,
} from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React, { useMemo, useState } from 'react';

import NetImages from '../../assets/images/homeRefactor';
import { useSendMessage } from '../../hooks/useSendMessage';
import { useAICallModalStore } from '../../store/aiCallModal';
import { EntryPoint, EntryPointType } from '../../types';
import { AICallRecordItem, ActionCardButton } from '../../types/message';
import { GradientText } from '../TaskList/AICallItem';

interface AICallRecordProps {
    data: {
        content: AICallRecordItem[];
        redirectButton: ActionCardButton;
        extendButtonName: string;
        showNum: number;
    };
}

const AICallRecord: React.FC<AICallRecordProps> = ({ data }) => {
    const [expanded, setExpanded] = useState(false);
    const { send } = useSendMessage();
    const openAICallModal = useAICallModalStore();

    const { content, redirectButton, extendButtonName, showNum } = data;

    const displayItems = useMemo(() => {
        if (!showNum || expanded) {
            return content;
        }
        return content.slice(0, showNum);
    }, [content, expanded, showNum]);

    const showExpand =
        Boolean(extendButtonName) && content.length > (showNum || 0);

    const handleButtonPress = (btn: ActionCardButton) => {
        if (btn.action === 'submitQuestion' && btn.question) {
            send(btn.question, EntryPointType.USER, EntryPoint.action_card);
            return;
        }
        if (btn.action === 'openAICallModal') {
            openAICallModal(btn.AICallParams || {});
            return;
        }
        if (btn.url) {
            openPage(btn.url);
        }
    };

    return (
        <View style={styles.container}>
            {displayItems.map((item, idx) => {
                return (
                    <View key={idx} style={styles.card}>
                        <View style={{ flex: 1 }}>
                            <View style={styles.header}>
                                <View style={styles.titleRow}>
                                    <Text
                                        style={styles.taskName}
                                        numberOfLines={1}
                                    >
                                        {item.jobName}
                                    </Text>
                                    {item.statusTextColor === '#running' ? (
                                        <View
                                            style={
                                                styles.runningStatusContainer
                                            }
                                        >
                                            <Image
                                                source={{ uri: NetImages.star }}
                                                style={styles.starIcon}
                                            />
                                            <GradientText
                                                text={item.statusText}
                                            />
                                        </View>
                                    ) : item.statusText ? (
                                        <TouchableOpacity
                                            style={[
                                                styles.statusContainer,
                                                item.button &&
                                                item.statusTextColor !==
                                                    '#running'
                                                    ? styles.clickableStatus
                                                    : null,
                                            ]}
                                            onPress={() => {
                                                if (
                                                    item.button &&
                                                    item.statusTextColor !==
                                                        '#running'
                                                ) {
                                                    handleButtonPress(
                                                        item.button,
                                                    );
                                                }
                                            }}
                                            disabled={
                                                !item.button ||
                                                item.statusTextColor ===
                                                    '#running'
                                            }
                                        >
                                            <Text
                                                style={[
                                                    styles.statusText,
                                                    item.statusTextColor &&
                                                    item.statusTextColor !==
                                                        '#running'
                                                        ? {
                                                              color: item.statusTextColor,
                                                          }
                                                        : {},
                                                ]}
                                            >
                                                {item.statusText}
                                            </Text>
                                            {item.button &&
                                                item.statusTextColor !==
                                                    '#running' && (
                                                    <Icon
                                                        type="right"
                                                        size={12}
                                                        tintColor="#666"
                                                    />
                                                )}
                                        </TouchableOpacity>
                                    ) : null}
                                </View>
                            </View>
                            <View>
                                {item.descriptions?.map((d, i) => (
                                    <View style={styles.descRow} key={i}>
                                        <Text style={styles.descLabel}>
                                            {d.label}：
                                        </Text>
                                        <Text style={styles.descValue}>
                                            {d.value}
                                        </Text>
                                    </View>
                                ))}
                            </View>
                        </View>
                    </View>
                );
            })}

            {expanded && redirectButton ? (
                <TouchableOpacity
                    style={[
                        styles.redirectButton,
                        redirectButton.type === 'primary'
                            ? styles.primaryButton
                            : styles.normalButton,
                    ]}
                    onPress={() => handleButtonPress(redirectButton)}
                    activeOpacity={0.8}
                >
                    <Text style={styles.buttonText}>{redirectButton.text}</Text>
                </TouchableOpacity>
            ) : null}

            {showExpand ? (
                <TouchableOpacity
                    style={styles.expandButton}
                    onPress={() => setExpanded((v) => !v)}
                >
                    <Text style={styles.expandText}>
                        共{content.length} {expanded ? '收起' : '展开'}
                    </Text>

                    <Icon
                        type={'down'}
                        size={10}
                        style={[
                            styles.expandIcon,
                            expanded && styles.expandIconRotated,
                        ]}
                    />
                </TouchableOpacity>
            ) : null}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        marginVertical: 4,
    },
    card: {
        backgroundColor: '#F5F6FA',
        borderRadius: 12,
        padding: 12,
        marginBottom: 8,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    header: {
        marginBottom: 4,
    },
    titleRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    taskName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#1f2329',
        flex: 1,
    },
    statusBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    descRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginTop: 4,
    },
    descLabel: {
        color: '#86909c',
        fontSize: 12,
        marginRight: 6,
        minWidth: 60,
    },
    descValue: {
        color: '#86909c',
        fontSize: 12,
    },
    button: {
        paddingVertical: 6,
        paddingHorizontal: 12,
        borderRadius: 14,
    },
    primaryButton: {
        backgroundColor: '#FFDD00',
        borderWidth: 1,
        borderColor: '#FFDD00',
    },
    normalButton: {
        backgroundColor: '#ffffff',
        borderWidth: 1,
        borderColor: '#e0e0e0',
    },
    buttonText: {
        color: '#1f2329',
        fontSize: 12,
        fontWeight: '500',
    },
    expandButton: {
        alignSelf: 'center',
        paddingVertical: 6,
        paddingHorizontal: 8,
        flexDirection: 'row',
        alignItems: 'center',
    },
    expandText: {
        color: '#86909c',
        fontSize: 12,
    },
    expandIcon: {
        marginLeft: 4,
        justifyContent: 'center',
        alignItems: 'center',
    },
    expandIconRotated: {
        transform: [{ rotate: '180deg' }],
    },
    expandArrow: {
        fontSize: 12,
        color: '#86909c',
    },
    runningStatusContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        backgroundColor: 'transparent',
    },
    starIcon: {
        width: 12,
        height: 12,
        marginRight: 4,
    },
    statusContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: 8,
    },
    statusText: {
        fontSize: 12,
        fontWeight: '400',
        color: '#86909c',
    },
    clickableStatus: {
        // 可点击状态无需特殊背景，只需确保触摸反馈
    },
    clickIcon: {
        fontSize: 14,
        color: '#999999',
        fontWeight: 'bold',
        marginLeft: 4,
    },
    redirectButton: {
        alignSelf: 'center',
        paddingVertical: 8,
        paddingHorizontal: 16,
        borderRadius: 14,
        marginTop: 8,
        width: '100%',
        alignItems: 'center',
    },
});

export default AICallRecord;
