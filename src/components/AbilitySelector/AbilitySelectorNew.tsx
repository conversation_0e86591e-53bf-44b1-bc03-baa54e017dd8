// 工具栏
import { useSafeAreaInsets } from '@mfe/bee-foundation-navigation';
import {
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from '@mrn/react-native';
import { Icon } from '@roo/roo-rn';
import React, { useMemo, useState, useRef } from 'react';

import Announcement from '../Chat/ChatFooter/Announcement';
import { useSkillClick } from '../Chat/ChatHome/ShortcutEntries';
import Condition from '../Condition/Condition';
import RNImage from '../RNImage';

import NetImages from '@/assets/images/homeRefactor';
import useAbsolutePosition from '@/hooks/useAbsolutePosition';
import { useHomePageData } from '@/hooks/useHomePageData';
import useMessage from '@/hooks/useMessage';
import { useUiState } from '@/store/uiState';
import TWS from '@/TWS';

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        overflow: 'visible', // 确保展开内容可见
    },
    absoluteContainer: {
        position: 'absolute',
        left: 0,
        right: 0,
        zIndex: 100,
        paddingHorizontal: 16,
        overflow: 'visible', // 确保展开内容不被裁剪
    },
    item: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 7,
        paddingHorizontal: 12,
        backgroundColor: '#fff',
        height: 36,
        borderRadius: 10,
    },
    dot: {
        width: 4,
        height: 4,
        borderRadius: 2,
        marginRight: 5,
    },
    expandIcon: {
        marginLeft: 4,
        width: 12,
        height: 12,
    },
    skillItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 8,
        marginBottom: 4,
        borderRadius: 8,
    },
    overlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 5,
    },
    portalContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 150,
    },
    portalExpandedContainer: {
        position: 'absolute',
        backgroundColor: '#fff',
        borderRadius: 10,
        elevation: 10,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        paddingVertical: 4,
        paddingHorizontal: 12,
        minWidth: 120,
    },
    subSkillsPanel: {
        position: 'absolute',
        backgroundColor: '#fff',
        borderRadius: 10,
        elevation: 12,
        shadowColor: '#000',
        shadowOffset: { width: 2, height: 2 },
        shadowOpacity: 0.15,
        shadowRadius: 6,
        paddingVertical: 8,
        paddingHorizontal: 12,
        minWidth: 140,
        maxWidth: 200,
        left: 150, // 在主面板右侧
    },
    subSkillItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 6,
        marginBottom: 2,
        borderRadius: 6,
    },
    subSkillHeader: {
        paddingBottom: 6,
        marginBottom: 6,
        borderBottomWidth: 1,
        borderBottomColor: '#E5E5E5',
    },
    subSkillHeaderText: {
        fontSize: 12,
        fontWeight: '600',
        color: '#333',
    },
});

const AbilitySelector = () => {
    const { skillGroups } = useHomePageData();
    const onSkillClick = useSkillClick('toolbar');
    const isWithFile = useMessage((state) => state.isWithFile);
    const [expandedGroupIndex, setExpandedGroupIndex] = useState<number | null>(
        null,
    );
    const [expandedMenuLayout, setExpandedMenuLayout] = useState<{
        x: number;
        y: number;
        width: number;
        height: number;
    } | null>(null);
    const buttonRefs = useRef<Array<View | null>>([]);

    // SubSkills 面板状态
    const [subSkillsPanelVisible, setSubSkillsPanelVisible] = useState(false);
    const [currentSubSkills, setCurrentSubSkills] = useState<any>(null);

    const { showHome } = useUiState();
    const absolutePosition = useAbsolutePosition();

    const finalOptions = useMemo(() => {
        // 只显示技能选项，不再处理 sceneTips
        return [
            ...skillGroups.filter((group) => group.type === 'group'),
            ...(skillGroups.find((group) => group.type === 'tiled')?.skills ||
                []),
        ] as any[];
    }, [skillGroups]);

    const handleGroupClick = (index: number) => {
        if (expandedGroupIndex === index) {
            // 关闭展开菜单
            console.log('Closing expanded menu');
            setExpandedGroupIndex(null);
            setExpandedMenuLayout(null);
        } else {
            // 测量按钮位置并打开展开菜单
            const buttonRef = buttonRefs.current[index];
            if (buttonRef) {
                buttonRef.measureInWindow((x, y, width, height) => {
                    const layout = {
                        x,
                        y,
                        width,
                        height,
                    };
                    console.log('Measured button layout:', layout);

                    setExpandedMenuLayout(layout);
                    setExpandedGroupIndex(index);
                });
            } else {
                console.warn('Button ref not found for index:', index);
            }
        }
    };

    // 处理技能点击，检查是否有 subSkills
    const handleSkillPress = (skill: any) => {
        if (skill.subSkillList && skill.subSkillList.length > 0) {
            // 有子技能，显示 subSkills 面板
            setCurrentSubSkills(skill);
            setSubSkillsPanelVisible(true);
        } else {
            // 没有子技能，执行正常点击
            onSkillClick(skill);
            setExpandedGroupIndex(null);
            setExpandedMenuLayout(null);
        }
    };

    // 处理 subSkill 点击
    const handleSubSkillPress = (subSkill: any) => {
        onSkillClick(subSkill);
        setSubSkillsPanelVisible(false);
        setExpandedGroupIndex(null);
        setExpandedMenuLayout(null);
    };

    // 关闭 subSkills 面板
    const closeSubSkillsPanel = () => {
        setSubSkillsPanelVisible(false);
        setCurrentSubSkills(null);
    };

    // 渲染 subSkills 面板
    const renderSubSkillsPanel = () => {
        if (
            !subSkillsPanelVisible ||
            !currentSubSkills ||
            !expandedMenuLayout
        ) {
            return null;
        }

        // 计算面板位置，在主面板右侧显示
        const panelLeft = expandedMenuLayout.x + 140; // 主面板宽度 + 间距
        const estimatedPanelHeight =
            currentSubSkills.subSkillList.length * 36 + 24; // 每个子技能项高度 + padding
        const panelTop = Math.max(
            10,
            expandedMenuLayout.y - estimatedPanelHeight - 8,
        );

        return (
            <View style={styles.portalContainer}>
                {/* 背景遮罩 */}
                <TouchableOpacity
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                    }}
                    activeOpacity={1}
                    onPress={() => {
                        closeSubSkillsPanel();
                        setExpandedGroupIndex(null);
                        setExpandedMenuLayout(null);
                    }}
                />

                {/* subSkills 面板 */}
                <View
                    style={[
                        styles.subSkillsPanel,
                        {
                            left: panelLeft,
                            top: panelTop - 25,
                        },
                    ]}
                >
                    {/* 面板标题 */}
                    <View style={styles.subSkillHeader}>
                        <Text style={styles.subSkillHeaderText}>
                            {currentSubSkills.content}
                        </Text>
                    </View>

                    {/* 子技能列表 */}
                    {currentSubSkills.subSkillList.map(
                        (subSkill: any, index: number) => (
                            <TouchableOpacity
                                key={subSkill.name || index}
                                style={styles.subSkillItem}
                                onPress={() => handleSubSkillPress(subSkill)}
                            >
                                {subSkill.link ? (
                                    <RNImage
                                        source={{ uri: subSkill.link }}
                                        style={[
                                            TWS.square(20),
                                            { marginRight: 6 },
                                        ]}
                                    />
                                ) : null}
                                <Text style={{ fontSize: 12, color: '#666' }}>
                                    {subSkill.content}
                                </Text>
                            </TouchableOpacity>
                        ),
                    )}
                </View>
            </View>
        );
    };

    const renderToolbarItem = (item: any, i) => {
        const isExpanded = item.type === 'group' && expandedGroupIndex === i;

        return (
            <TouchableOpacity
                key={item.name}
                ref={(ref) => {
                    if (item.type === 'group') {
                        buttonRefs.current[i] = ref;
                    }
                }}
                style={[
                    styles.item,
                    { marginRight: i === finalOptions.length - 1 ? 0 : 10 },
                ]}
                onPress={() => {
                    if (item?.type === 'group') {
                        handleGroupClick(i);
                    } else {
                        onSkillClick(item);
                    }
                }}
            >
                {item?.link ? (
                    <RNImage
                        source={{
                            uri: item.link,
                        }}
                        style={[
                            item.type !== 'group'
                                ? TWS.square(16)
                                : { height: 13 },
                            { marginRight: 2 },
                        ]}
                    />
                ) : null}
                <Condition condition={[item.type !== 'group']}>
                    <Text style={{ fontWeight: '500' }}>{item.content}</Text>
                </Condition>
                <Condition condition={[item.type === 'group']}>
                    <RNImage
                        source={NetImages.down}
                        style={{
                            width: 12,
                            transform: [
                                { rotate: isExpanded ? '0deg' : '180deg' },
                            ],
                        }}
                    />
                </Condition>
            </TouchableOpacity>
        );
    };

    const insets = useSafeAreaInsets();

    // 渲染Portal层的展开菜单
    const renderPortalExpandedMenu = () => {
        if (expandedGroupIndex === null || !expandedMenuLayout) {
            console.log('Portal menu not rendering: missing index or layout');
            return null;
        }

        const expandedGroup = finalOptions[expandedGroupIndex];
        if (!expandedGroup?.skills) {
            console.log('Portal menu not rendering: no skills in group');
            return null;
        }

        // 计算菜单位置 - 估算菜单高度
        const estimatedMenuHeight = expandedGroup.skills.length * 44 + 16; // 每个技能项44px高度 + padding
        const menuTop = Math.max(
            10,
            expandedMenuLayout.y - estimatedMenuHeight - 8,
        ); // 确保不超出屏幕顶部

        return (
            <View style={styles.portalContainer}>
                {/* 背景遮罩，点击关闭菜单 */}
                <TouchableOpacity
                    style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                    }}
                    activeOpacity={1}
                    onPress={() => {
                        setExpandedGroupIndex(null);
                        setExpandedMenuLayout(null);
                    }}
                />

                {/* 展开的菜单内容 */}
                <View
                    style={[
                        styles.portalExpandedContainer,
                        {
                            left: expandedMenuLayout.x,
                            top: Platform.select({
                                default: menuTop - 10,
                                ios: menuTop - insets.top - 10,
                            }),
                        },
                    ]}
                >
                    {expandedGroup.skills.map(
                        (skill: any, skillIndex: number) => (
                            <TouchableOpacity
                                key={skill.name || skillIndex}
                                style={styles.skillItem}
                                onPress={() => handleSkillPress(skill)}
                            >
                                {skill.link ? (
                                    <RNImage
                                        source={{ uri: skill.link }}
                                        style={[
                                            TWS.square(28),
                                            { marginRight: 8 },
                                        ]}
                                    />
                                ) : null}
                                <Text style={{ fontSize: 14, color: '#666' }}>
                                    {skill.content}
                                </Text>
                                {skill.subSkillList &&
                                    skill.subSkillList.length > 0 && (
                                        <Icon
                                            type="right"
                                            size={12}
                                            tintColor="#999"
                                            style={{ marginLeft: 'auto' }}
                                        />
                                    )}
                            </TouchableOpacity>
                        ),
                    )}
                </View>
            </View>
        );
    };

    if (isWithFile()) {
        return null;
    }

    return (
        <>
            {/* 公告组件独立渲染，不受 finalOptions 条件限制 */}
            <View
                style={[
                    styles.absoluteContainer,
                    {
                        bottom: absolutePosition.bottom,
                        zIndex: 120,
                    },
                ]}
            >
                <Announcement
                    style={{
                        zIndex: 105,
                    }}
                />
            </View>

            {/* Toolbar 主体 */}
            <Condition condition={[!showHome && finalOptions.length > 0]}>
                <View
                    style={[
                        styles.absoluteContainer,
                        absolutePosition,
                        { backgroundColor: '#f5f6fa', paddingTop: 8 },
                    ]}
                >
                    <ScrollView
                        horizontal
                        contentContainerStyle={styles.container}
                        showsHorizontalScrollIndicator={false}
                        keyboardShouldPersistTaps="handled"
                        style={{ overflow: 'visible' }} // 确保展开内容不被裁剪
                    >
                        {finalOptions?.map(renderToolbarItem)}
                    </ScrollView>
                </View>
            </Condition>

            {/* Portal层的展开菜单 - 独立于条件渲染 */}
            {renderPortalExpandedMenu()}

            {/* SubSkills 面板 */}
            {renderSubSkillsPanel()}
        </>
    );
};

export default AbilitySelector;
