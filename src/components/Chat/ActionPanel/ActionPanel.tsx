import { deviceEventEmitter } from '@mrn/mrn-utils';
import { TouchableOpacity, Text, Image, View } from '@mrn/react-native';
import { Toast } from '@roo/roo-rn';
import React, { useEffect, useState } from 'react';

import { useUiState } from '../../../store/uiState';
import TWS from '../../../TWS';

import NetImages from '@/assets/images/homeRefactor';
import Condition from '@/components/Condition/Condition';
import uploadImage from '@/components/imageUpload/uploadImage';
import Dot from '@/components/MessageBox/Answer/AnswerContent/SelectionMessage/Dot';
import useMockMode from '@/hooks/mock/useMockMode';
import useMessage from '@/hooks/useMessage';
import useStorage from '@/hooks/useStorage';
import useTrace from '@/hooks/useTrace';
import useInteractionGray from '@/pages/chat/hooks/useInteractionGray';
import { trackButtonClick } from '@/utils/track';

const ActionItem = ({ text, icon, onPress }) => {
    const [notificationBadge, setNotificationBadge] =
        useStorage('notificationBadge');

    const [_, setForceUpdate] = useState(0);

    useEffect(() => {
        const sub1 = deviceEventEmitter.addListener(
            'containerViewDidAppear',
            () => {
                setForceUpdate((v) => v + 1);
            },
        );
        return () => {
            sub1.remove();
        };
    }, []);

    const { isGray } = useInteractionGray();

    return (
        <TouchableOpacity
            onPress={() => {
                onPress();
                setNotificationBadge({
                    ...notificationBadge,
                    tools: notificationBadge?.tools?.filter(
                        (tool) => tool !== text,
                    ),
                });
            }}
            style={{
                backgroundColor: 'white',
                width: 77,
                height: 77,
                borderRadius: 10,
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 14.5,
                position: 'relative',
            }}
        >
            <Image
                source={{ uri: icon }}
                style={[TWS.square(24), { marginBottom: 4 }]}
            />
            <Text style={{ fontSize: 12 }}>{text}</Text>
            <Condition condition={[notificationBadge?.tools?.includes(text)]}>
                <Dot
                    width={10}
                    style={[
                        { position: 'absolute' },
                        isGray
                            ? { right: -4, top: -4 }
                            : { right: 12, top: 12 },
                    ]}
                />
            </Condition>
        </TouchableOpacity>
    );
};

// Mock模式开关组件
const MockActionItem = ({ onPress, isMockMode }) => {
    return (
        <TouchableOpacity
            onPress={onPress}
            style={{
                backgroundColor: isMockMode ? '#52c41a' : 'white',
                width: 77,
                height: 77,
                borderRadius: 10,
                borderWidth: isMockMode ? 0 : 1,
                borderColor: '#d9d9d9',
                alignItems: 'center',
                justifyContent: 'center',
                marginRight: 14.5,
                position: 'relative',
            }}
        >
            <Text
                style={{
                    fontSize: 20,
                    fontWeight: 'bold',
                    color: isMockMode ? 'white' : '#666',
                    marginBottom: 4,
                }}
            >
                M
            </Text>
            <Text
                style={{
                    fontSize: 12,
                    color: isMockMode ? 'white' : '#666',
                }}
            >
                Mock
            </Text>
            <Condition condition={[isMockMode]}>
                <View
                    style={{
                        position: 'absolute',
                        top: 4,
                        right: 4,
                        width: 8,
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: '#fff',
                    }}
                />
            </Condition>
        </TouchableOpacity>
    );
};

const TraceMap = {
    选择商家: 'poi_selector',
    相册: 'photograph',
    拍照: 'take_photo',
};
const ActionPanel = () => {
    const { panelOpen, setPanelOpen, setPanelHeight } = useUiState();
    const { pictureQuestionGray } = useInteractionGray();
    const setFile = useMessage((state) => state.setFile);
    const updateFile = useMessage((state) => state.updateFile);
    const file = useMessage((state) => state.file);

    const inputState = useMessage((state) => state.input);
    const trace = useTrace();

    // Mock模式相关
    const { isMockMode, enableMockMode, disableMockMode } = useMockMode();
    let actions = [
        // {
        //     text: '选择商家',
        //     icon: NetImages.merchantIcon,
        //     onPress: () => {
        //         trace(TraceMap['选择商家'], 'trigger');
        //         trackButtonClick('poi_selector');
        //         setPoiSelectorOpen(true);
        //         setPanelOpen(false);
        //     },
        // },
    ];
    const onUploadImageSuccess = () => {
        setTimeout(() => {
            inputState?.focus?.();
        }, 250);
    };
    const onUploadPress = (type: '相册' | '拍照') => {
        if (file.length > 0) {
            Toast.open('仅允许发送单张图片');
            return;
        }
        trace(TraceMap[type], 'trigger');
        trackButtonClick(TraceMap[type]);
        let key: string;
        uploadImage({
            sourceType: type === '相册' ? ['album'] : ['camera'],
            onSuccess: (data) => {
                updateFile(key, {
                    status: 'success',
                    ...data,
                });
                onUploadImageSuccess();
            },
            onChooseSuccess: (tempFilePaths) => {
                setPanelOpen(false);
                const keys = setFile([
                    {
                        type: 'image',
                        localSrc: tempFilePaths[0],
                        status: 'uploading',
                    },
                ]);
                key = keys[0];
            },
        });
    };
    if (pictureQuestionGray) {
        actions = [
            ...actions,
            {
                text: '相册',
                icon: NetImages.albumIcon,
                onPress: () => onUploadPress('相册'),
            },
            {
                text: '拍照',
                icon: NetImages.cameraIcon,
                onPress: () => onUploadPress('拍照'),
            },
        ];
    }

    // Mock模式切换处理
    const handleMockToggle = () => {
        if (isMockMode) {
            disableMockMode();
            Toast.open('Mock模式已关闭');
        } else {
            enableMockMode();
            Toast.open('Mock模式已开启');
        }
        setPanelOpen(false);
    };

    if (!panelOpen) {
        return null;
    }
    return (
        <View
            onTouchStart={(e) => {
                e.stopPropagation();
            }}
            onLayout={(event) => {
                const { height } = event.nativeEvent.layout;
                setPanelHeight(height);
            }}
            style={[
                {
                    padding: 20,
                    flexWrap: 'wrap',
                    flexDirection: 'row',
                },
            ]}
        >
            {actions.map((v) => (
                <ActionItem {...v} key={v.text} />
            ))}
            <Condition condition={[__DEV__]}>
                <MockActionItem
                    onPress={handleMockToggle}
                    isMockMode={isMockMode}
                />
            </Condition>
        </View>
    );
};
export default ActionPanel;
