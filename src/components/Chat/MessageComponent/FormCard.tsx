import {
    View,
    Text,
    StyleSheet,
    ViewStyle,
    TextStyle,
    TouchableOpacity,
    TextInput,
    LayoutChangeEvent,
} from '@mrn/react-native';
import { LinearGradient } from '@mrn/react-native-linear-gradient';
import { Icon, Toast } from '@roo/roo-rn';
import React, { useContext, useEffect, useState, useRef } from 'react';

import { useSendMessage } from '../../../hooks/useSendMessage';
import { EntryPoint } from '../../../types';

import Condition from '@/components/Condition/Condition';
import { AnswerContext } from '@/components/MessageBox/Answer/AnswerContext';

interface RadioProps {
    value: string;
    selected?: boolean;
    onPress?: () => void;
    style?: ViewStyle;
    children?: React.ReactNode;
    disabled?: boolean;
    isLast?: boolean;
    isFirst?: boolean;
}

const Radio: React.FC<RadioProps> = ({
    selected,
    onPress,
    style,
    children,
    disabled,
    isLast,
}) => {
    return (
        <TouchableOpacity
            style={[
                styles.radioButton,
                selected && styles.radioButtonSelected,
                disabled && styles.radioButtonDisabled,
                isLast && { marginRight: 0 },
                style,
            ]}
            onPress={onPress}
            activeOpacity={0.8}
            disabled={disabled}
        >
            <Text
                style={[
                    styles.radioText,
                    disabled && styles.radioTextDisabled,
                    selected && styles.radioTextSelected,
                ]}
            >
                {children}
            </Text>
        </TouchableOpacity>
    );
};

interface RadioGroupProps {
    value?: string;
    onChange?: (value: string) => void;
    style?: ViewStyle | ViewStyle[];
    children?: React.ReactNode;
    disabled?: boolean;
}

const RadioGroup: React.FC<RadioGroupProps> = ({
    value,
    onChange,
    style,
    children,
    disabled,
}) => {
    return (
        <View
            style={[
                styles.radioGroup,
                ...(Array.isArray(style) ? style : [style]),
            ]}
        >
            {React.Children.map(children, (child, index) => {
                if (React.isValidElement<RadioProps>(child)) {
                    return React.cloneElement(child, {
                        selected: child.props.value === value,
                        onPress: () => onChange?.(child.props.value),
                        disabled,
                        isLast: index === (children as any[])?.length - 1,
                        isFirst: index === 0,
                    });
                }
                return child;
            })}
        </View>
    );
};

interface FormCardProps {
    config: {
        label: string;
        type: 'radio' | 'input';
        options?: string[]; // input不需要
        defaultValue?: string; // 默认值
        tooltip?: string; // label后的问号的提示词，蜜蜂端点击弹窗展示，pc端hover展示
        labelWrap?: boolean; // 是否换行
        regExp?: string; // 正则校验规则，任一项不通过则提示messge并拦截按钮操作
        message?: string; // 错误提示
        required?: boolean; // 是否必填，默认true，除非明确设置为false
    }[];
    buttonText?: string;
    history?: boolean;
    title?: string; // 标题
    subTitle?: string; // 副标题
    labelSpan?: number; // label占行宽的比例，如果为3则占行宽 3/24 = 1/8
}

export const FormCard: React.FC<FormCardProps> = ({
    config,
    buttonText = '确定',
    history: _history,
    title,
    subTitle,
}) => {
    const [values, setValues] = useState<Record<string, string>>(() => {
        const initialValues: Record<string, string> = {};
        config.forEach((item) => {
            initialValues[item.label] = item.defaultValue || '';
        });
        return initialValues;
    });
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [maxLabelWidth, setMaxLabelWidth] = useState(0);
    const labelWidths = useRef<number[]>([]);

    const { setWithForm } = useContext(AnswerContext);
    useEffect(() => {
        setWithForm(true);
    }, []);

    const { send } = useSendMessage();

    // 提取公共的表单验证逻辑
    const validateFormItems = (
        values: Record<string, string>,
    ): { errors: Record<string, string>; isValid: boolean } => {
        const newErrors: Record<string, string> = {};
        let isValid = true;

        config.forEach((item) => {
            const value = values[item.label];

            // 如果有正则校验规则
            if (item.regExp && value) {
                try {
                    const regex = new RegExp(item.regExp);
                    if (!regex.test(value)) {
                        newErrors[item.label] =
                            item.message || `${item.label}格式不正确`;
                        isValid = false;
                    }
                } catch (error) {
                    console.warn('Invalid regex pattern:', item.regExp);
                }
            }

            // 如果有必填项校验（默认为true，除非明确设置为false）
            if (item.required !== false && !value) {
                newErrors[item.label] = item.message || `${item.label}不能为空`;
                isValid = false;
            }
        });

        return { errors: newErrors, isValid };
    };

    // 表单验证函数
    const validateForm = (): boolean => {
        const { errors: newErrors, isValid } = validateFormItems(values);
        setErrors(newErrors);
        return isValid;
    };

    const handleSubmit = () => {
        // 先进行表单验证
        if (!validateForm()) {
            // 显示第一个错误信息
            const firstError = Object.values(errors)[0];
            if (firstError) {
                Toast.open(firstError);
            }
            return;
        }

        setIsSubmitted(true);
        const message = Object.entries(values)
            .map(([label, value]) => `${label}: ${value}`)
            .join('\n');
        send(message, undefined, EntryPoint.form_input);
    };

    const handleChange = (label: string, value: string) => {
        const newValues = {
            ...values,
            [label]: value,
        };

        setValues(newValues);

        // 立即验证新值并更新错误状态
        const { errors: newErrors } = validateFormItems(newValues);
        setErrors(newErrors);
    };

    const isDisabled = _history || isSubmitted;

    const handleLabelLayout = (index: number) => (event: LayoutChangeEvent) => {
        const { width } = event.nativeEvent.layout;
        labelWidths.current[index] = width;
        const maxWidth = Math.max(...labelWidths.current.filter(Boolean));
        if (maxWidth > maxLabelWidth) {
            setMaxLabelWidth(maxWidth);
        }
    };

    return (
        <View style={styles.container}>
            <Condition condition={[title]}>
                <View style={{ position: 'relative', marginBottom: 4 }}>
                    <Text
                        style={{
                            fontSize: 16,
                            fontWeight: '700',
                            zIndex: 1,
                            color: '#222',
                        }}
                    >
                        {title}
                    </Text>
                    {/* <RNImage
                        source={{ uri: NetImages.yellowBall }}
                        style={{
                            width: 30,
                            height: 16,
                            position: 'absolute',
                            left: 16,
                            top: 0,
                        }}
                    /> */}
                </View>
            </Condition>
            <Condition condition={[subTitle]}>
                <Text style={{ fontSize: 12, color: '#999', marginBottom: 16 }}>
                    {subTitle}
                </Text>
            </Condition>
            {config.map((item, index) => {
                return (
                    <View
                        key={index}
                        style={[
                            styles.formItem,
                            item.labelWrap && {
                                flexDirection: 'column',
                                alignItems: undefined,
                                justifyContent: 'center',
                            },
                        ]}
                    >
                        <View
                            style={[
                                {
                                    flexDirection: 'row',
                                    marginRight: 4,
                                    alignItems: 'center',
                                    height: 40,
                                    width:
                                        maxLabelWidth > 0
                                            ? maxLabelWidth
                                            : undefined,
                                },
                                item.labelWrap && { marginBottom: 8 },
                            ]}
                            onLayout={handleLabelLayout(index)}
                        >
                            <Text style={[styles.label]}>{item.label}</Text>
                            <Condition condition={[item.tooltip]}>
                                <TouchableOpacity
                                    onPress={() => {
                                        Toast.open(item.tooltip);
                                    }}
                                >
                                    <Icon
                                        type="question-circle-o"
                                        size={16}
                                        tintColor="#999"
                                    />
                                </TouchableOpacity>
                            </Condition>
                        </View>

                        <Condition
                            condition={[
                                item.type === 'input',
                                item.type === 'radio',
                            ]}
                        >
                            <View style={{ flex: 1 }}>
                                <TextInput
                                    style={[
                                        styles.input,
                                        isDisabled && styles.inputDisabled,
                                        errors[item.label] && styles.inputError,
                                    ]}
                                    value={values[item.label]}
                                    onChangeText={(text) =>
                                        handleChange(item.label, text)
                                    }
                                    placeholder={`请输入${item.label}`}
                                    placeholderTextColor="#999999"
                                    editable={!isDisabled}
                                />
                                {errors[item.label] ? (
                                    <Text style={styles.errorText}>
                                        {errors[item.label]}
                                    </Text>
                                ) : null}
                            </View>

                            <View style={{ flex: 1 }}>
                                <RadioGroup
                                    value={values[item.label]}
                                    onChange={(value) =>
                                        handleChange(item.label, value)
                                    }
                                    style={[styles.radioGroup]}
                                    disabled={isDisabled}
                                >
                                    {item.options?.map(
                                        (option, optionIndex) => (
                                            <Radio
                                                key={optionIndex}
                                                value={option}
                                            >
                                                {option}
                                            </Radio>
                                        ),
                                    )}
                                </RadioGroup>
                                {errors[item.label] && (
                                    <Text style={styles.errorText}>
                                        {errors[item.label]}
                                    </Text>
                                )}
                            </View>
                        </Condition>
                    </View>
                );
            })}
            {!isDisabled && (
                <TouchableOpacity
                    onPress={handleSubmit}
                    style={{
                        height: 42,
                        borderRadius: 21,
                        overflow: 'hidden',
                        width: '100%',
                    }}
                >
                    <LinearGradient
                        colors={['#4021FF', '#752FFF']}
                        style={{
                            height: 42,
                            borderRadius: 21,
                            justifyContent: 'center',
                            alignItems: 'center',
                            flex: 1,
                        }}
                    >
                        <Text style={styles.submitButtonText}>
                            {buttonText}
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        borderRadius: 8,
        padding: 4,
    },
    formItem: {
        marginBottom: 16,
        flexDirection: 'row',
    },
    label: {
        fontSize: 14,
        color: '#222222',
        flexShrink: 1,
    },
    input: {
        flex: 1,
        height: 40,
        borderWidth: 1,
        borderColor: '#E8E8E8',
        borderRadius: 8,
        paddingHorizontal: 12,
        fontSize: 12,
        color: '#222222',
        backgroundColor: '#F5F6FA',
    },
    inputDisabled: {
        backgroundColor: '#F5F5F5',
        borderColor: '#E8E8E8',
        color: '#999999',
    },
    inputError: {
        borderColor: '#FF192D',
    },
    radioGroup: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        flex: 1,
    },
    radioButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        height: 40,
        paddingVertical: 4,
        paddingHorizontal: 12,
        backgroundColor: '#F5F6FA',
        borderRadius: 8,
        marginRight: 8,
        marginBottom: 8,
        flex: 1,
        borderWidth: 1,
        borderColor: 'transparent',
    },
    radioButtonSelected: {
        backgroundColor: '#FFFBE0',
        borderColor: '#FFDD00',
    },
    radioButtonDisabled: {
        opacity: 0.5,
    },
    radioCircle: {
        width: 16,
        height: 16,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#E8E8E8',
        marginRight: 8,
    },
    radioCircleSelected: {
        backgroundColor: '#FFFFFF',
        borderColor: '#FFFFFF',
    },
    radioCircleDisabled: {
        backgroundColor: '#E8E8E8',
        borderColor: '#E8E8E8',
    },
    radioText: {
        fontSize: 12,
        color: '#222222',
    },
    radioTextDisabled: {
        color: '#999999',
    },
    radioTextSelected: {
        color: '#222',
    },
    submitButton: {
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
    },
    submitButtonText: {
        fontSize: 14,
        color: '#FFF',
        fontWeight: '500',
    },
    errorText: {
        fontSize: 12,
        color: '#FF4D4F',
        marginTop: 4,
    },
    tooltipContent: {
        backgroundColor: '#FFFFFF',
        borderRadius: 8,
        width: 200,
        padding: 16,
    },
} as Record<string, ViewStyle | TextStyle>);
