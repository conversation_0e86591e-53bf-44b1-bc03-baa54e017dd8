import { Keyboard, TextInput } from '@mrn/react-native';
import React, { useCallback, useEffect, useRef } from 'react';

import useMessage from '../../../../hooks/useMessage';
import { useSendMessage } from '../../../../hooks/useSendMessage';
import { EntryPointType } from '../../../../types';
import { trackEvent } from '../../../../utils/track';

import { getAdditionMessage } from '@/utils/message/getAdditionMessage';

const AiTextInput = (props) => {
    const {
        onBlur = () => {},
        onFocus = () => {},
        style = {},
        onTextChange = () => {},
    } = props;
    const {
        text: input,
        set: setInput,
        clear,
        setInputState,
    } = useMessage((state) => state.input);
    const inputRef = useRef<TextInput>();
    const checkIsPolling = useMessage((state) => state.checkIsPolling);
    const { send } = useSendMessage();
    const { send: sendAdditionMessage } = useSendMessage();
    const file = useMessage((state) => state.file);
    const clearFile = useMessage((state) => state.clearFile);
    const onSend = useCallback(() => {
        if (checkIsPolling()) {
            return;
        }

        if (file?.length) {
            sendAdditionMessage(
                getAdditionMessage(file, input),
                EntryPointType.TOOL,
                'picture_tip',
            );
            clearFile();
        } else {
            send(input, EntryPointType.USER);
        }
        clear();
    }, [input, file]);

    useEffect(() => {
        setInputState({
            blur: () => inputRef.current?.blur?.(),
            focus: () => inputRef.current?.focus?.(),
            send: onSend,
        });
    }, [inputRef, onSend]);

    const scrollToEnd = useMessage((state) => state.delayScrollToEnd);
    const onInputFocus = () => {
        trackEvent('chat_input');
        onFocus();
        scrollToEnd(500, true);
    };

    useEffect(() => {
        const sub = Keyboard.addListener('keyboardDidHide', () => {
            inputRef.current?.blur();
        });
        return () => {
            sub.remove();
        };
    }, []);

    return (
        <TextInput
            ref={inputRef}
            value={input}
            onChangeText={(v) => {
                setInput(v);
                onTextChange(v);
            }}
            style={[
                {
                    flex: 1,
                    height: 40,
                },
                style,
            ]}
            placeholder="有什么问题尽管问我"
            blurOnSubmit={false}
            maxLength={100}
            onFocus={onInputFocus}
            onBlur={onBlur}
            returnKeyType={'send'}
            returnKeyLabel={'发送'}
            onSubmitEditing={onSend}
        />
    );
};
export default AiTextInput;
