import { ScrollView, StyleSheet, View } from '@mrn/react-native';
import React from 'react';

import GreetingBanner from './GreetingBanner';
import ShortcutEntries from './ShortcutEntries';
import SkillCards from './SkillCards';

import { useHomePageData } from '@/hooks/useHomePageData';

interface ChatHomeProps {
    showGuide?: boolean;
    guideSteps?: Array<{
        key: string;
        title: string;
        description: string;
    }>;
    onGuideClose?: () => void;
    onGuideComplete?: () => void;
    wrapWithTooltip?: (
        key: string,
        children: React.ReactNode,
    ) => React.ReactNode;
}

const ChatHome: React.FC<ChatHomeProps> = ({ wrapWithTooltip }) => {
    const { skillGroups } = useHomePageData();
    const tiledData = skillGroups.filter((v) => v.type === 'tiled');
    return (
        <View style={styles.wrapper}>
            <ScrollView
                style={styles.container}
                showsVerticalScrollIndicator={false}
            >
                <GreetingBanner />
                <SkillCards wrapWithTooltip={wrapWithTooltip} />
                {tiledData?.map((data, index) => {
                    // 特殊处理，只有5001才有引导，此时其需要引导的数据为第一个
                    if (index === 0) {
                        return wrapWithTooltip(
                            'shortcuts',
                            <ShortcutEntries shortcutSkills={data.skills} />,
                        );
                    }
                    return <ShortcutEntries shortcutSkills={data.skills} />;
                })}
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    wrapper: {
        flex: 1,
        position: 'relative',
    },
    container: {
        flex: 1,
    },
});

export default ChatHome;
