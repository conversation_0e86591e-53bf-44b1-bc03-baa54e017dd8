import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Modal,
    Dimensions,
    Image,
} from '@mrn/react-native';
import React from 'react';

import {
    AccessibilityEnhancement,
    TouchFeedback,
} from './AccessibilityEnhancement';

import { Skill } from '@/types/homePageApi';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface SubSkillPanelProps {
    /** 是否显示面板 */
    visible: boolean;
    /** 父技能信息 */
    parentSkill: Skill;
    /** 子技能列表 */
    subSkills: Skill[];
    /** 关闭面板回调 */
    onClose: () => void;
    /** 子技能点击回调 */
    onSubSkillPress: (skill: Skill) => void;
    /** 父技能的位置信息 */
    parentSkillPosition?: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
}

interface SubSkillItemProps {
    skill: Skill;
    onPress: (skill: Skill) => void;
}

/**
 * 子技能项组件
 */
const SubSkillItem: React.FC<SubSkillItemProps> = ({ skill, onPress }) => {
    return (
        <TouchableOpacity
            style={styles.subSkillItem}
            onPress={() => onPress(skill)}
            {...TouchFeedback.getButtonProps()}
            {...AccessibilityEnhancement.createAccessibilityProps({
                label: skill.content,
                hint: skill.isNew ? '新功能，点击了解详情' : '点击了解详情',
                role: 'button',
            })}
        >
            {/* 技能图标 */}
            {skill.link ? (
                <Image
                    source={{ uri: skill.link }}
                    style={styles.subSkillIcon}
                    resizeMode="contain"
                />
            ) : null}

            {/* 技能名称 */}
            <Text style={styles.subSkillText} numberOfLines={2}>
                {skill.content}
            </Text>

            {/* 新功能标识 */}
            {skill.isNew ? (
                <View
                    style={styles.newBadge}
                    {...AccessibilityEnhancement.createAccessibilityProps({
                        label: '新功能标识',
                        role: 'text',
                    })}
                >
                    <Text style={styles.newBadgeText}>新</Text>
                </View>
            ) : null}
        </TouchableOpacity>
    );
};

/**
 * 子技能展开面板组件
 * 根据UI稿实现子技能选择面板，点击技能项后会展开一个选择的面板
 */
const SubSkillPanel: React.FC<SubSkillPanelProps> = ({
    visible,
    parentSkill,
    subSkills,
    onClose,
    onSubSkillPress,
    parentSkillPosition,
}) => {
    const handleSubSkillPress = (skill: Skill) => {
        onSubSkillPress(skill);
        // 注意：不在这里调用 onClose()，交给外层组件处理
    };

    const handleBackdropPress = () => {
        onClose();
    };

    // 计算面板位置
    const getPanelPosition = () => {
        if (!parentSkillPosition) {
            return {
                justifyContent: 'center' as const,
                alignItems: 'center' as const,
            };
        }

        // 根据子技能数量动态估算面板尺寸
        const subSkillCount = subSkills?.length || 0;
        const itemHeight = 32 + 4; // 每个技能项高度 + marginBottom
        const panelPadding = 8 + 8; // 上下内边距
        const panelHeight = Math.min(
            subSkillCount * itemHeight + panelPadding,
            300,
        ); // 最大高度限制

        // 根据内容估算面板宽度
        const maxTextLength = Math.max(
            ...(subSkills?.map((skill) => skill.content.length) || [0]),
        );
        const estimatedWidth = Math.min(
            Math.max(120, maxTextLength * 14 + 40),
            200,
        ); // 动态宽度，介于120-180之间

        const marginSafe = 16; // 安全边距

        const spaceAbove = parentSkillPosition.y;
        const spaceBelow =
            screenHeight - (parentSkillPosition.y + parentSkillPosition.height);

        // 默认显示在上方，如果空间不够则显示在下方
        const showAbove = spaceAbove >= panelHeight || spaceAbove > spaceBelow;

        // 计算水平位置，防止溢出屏幕
        let horizontalPosition = parentSkillPosition.x;

        // 尝试与父技能居中对齐（如果空间足够）
        const parentCenter =
            parentSkillPosition.x + parentSkillPosition.width / 2;
        const centeredPosition = parentCenter - estimatedWidth / 2;

        // 检查居中位置是否合适
        if (
            centeredPosition >= marginSafe &&
            centeredPosition + estimatedWidth <= screenWidth - marginSafe
        ) {
            // 居中位置合适，使用居中对齐
            horizontalPosition = centeredPosition;
        } else {
            // 居中位置不合适，使用左对齐并检查溢出
            // 检查右侧溢出
            if (
                horizontalPosition + estimatedWidth >
                screenWidth - marginSafe
            ) {
                // 右侧溢出，调整到屏幕右边界内
                horizontalPosition = screenWidth - estimatedWidth - marginSafe;
            }

            // 检查左侧溢出
            if (horizontalPosition < marginSafe) {
                // 左侧溢出，调整到屏幕左边界内
                horizontalPosition = marginSafe;
            }
        }

        if (showAbove) {
            return {
                justifyContent: 'flex-start' as const,
                alignItems: 'flex-start' as const,
                paddingTop: Math.max(
                    20,
                    parentSkillPosition.y - panelHeight - 10,
                ),
                paddingLeft: horizontalPosition,
            };
        } else {
            return {
                justifyContent: 'flex-start' as const,
                alignItems: 'flex-start' as const,
                paddingTop:
                    parentSkillPosition.y + parentSkillPosition.height + 10,
                paddingLeft: horizontalPosition,
            };
        }
    };

    const positionStyle = getPanelPosition();

    return (
        <Modal
            visible={visible}
            transparent
            animationType="fade"
            onRequestClose={onClose}
            {...AccessibilityEnhancement.createAccessibilityProps({
                label: `${parentSkill.content}子技能选择面板`,
                role: 'dialog',
            })}
        >
            {/* 背景遮罩 */}
            <TouchableOpacity
                style={[styles.backdrop, positionStyle]}
                activeOpacity={1}
                onPress={handleBackdropPress}
                {...AccessibilityEnhancement.createAccessibilityProps({
                    label: '关闭面板',
                    hint: '点击背景关闭子技能选择面板',
                    role: 'button',
                })}
            >
                {/* 面板内容区域 */}
                <TouchableOpacity
                    style={styles.panelContainer}
                    activeOpacity={1}
                    onPress={(e) => e.stopPropagation()} // 阻止事件冒泡
                >
                    <View style={styles.panel}>
                        {/* 子技能列表 */}
                        <View style={styles.subSkillList}>
                            {subSkills && subSkills.length > 0 ? (
                                subSkills.map((skill, index) => (
                                    <SubSkillItem
                                        key={`sub-skill-${index}`}
                                        skill={skill}
                                        onPress={handleSubSkillPress}
                                    />
                                ))
                            ) : (
                                <Text style={styles.emptyText}>暂无子技能</Text>
                            )}
                        </View>
                    </View>
                </TouchableOpacity>
            </TouchableOpacity>
        </Modal>
    );
};

const styles = StyleSheet.create({
    backdrop: {
        flex: 1,
        backgroundColor: 'transparent',
    },
    panelContainer: {
        minWidth: 120,
        maxWidth: 200,
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 6,
    },
    panel: {
        paddingVertical: 8,
    },
    subSkillList: {
        paddingVertical: 4,
    },
    subSkillItem: {
        width: '100%',
        backgroundColor: 'transparent',
        borderRadius: 8,
        paddingVertical: 8,
        paddingHorizontal: 12,
        marginBottom: 4,
        minHeight: 32,
        flexDirection: 'row',
        alignItems: 'center',
        position: 'relative',
    },
    subSkillIcon: {
        width: 16,
        height: 16,
        marginRight: 8,
    },
    subSkillText: {
        fontSize: 13,
        color: '#333',
        fontWeight: '400',
        lineHeight: 16,
        flex: 1,
    },
    newBadge: {
        position: 'absolute',
        top: 4,
        right: 4,
        backgroundColor: '#FF6A00',
        borderRadius: 5,
        paddingHorizontal: 3,
        paddingVertical: 1,
        minWidth: 10,
        height: 10,
        justifyContent: 'center',
        alignItems: 'center',
    },
    newBadgeText: {
        fontSize: 7,
        color: '#FFFFFF',
        fontWeight: '600',
    },
    emptyText: {
        fontSize: 12,
        color: '#999',
        textAlign: 'center',
        width: '100%',
        paddingVertical: 12,
    },
});

export default SubSkillPanel;
