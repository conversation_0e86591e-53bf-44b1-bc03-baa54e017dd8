import { View, Text, TouchableOpacity, StyleSheet } from '@mrn/react-native';
import { LinearGradient } from '@mrn/react-native-linear-gradient';
import { Icon } from '@roo/roo-rn';
import React, { useMemo } from 'react';

import NetImages from '@/assets/images/homeRefactor';
import RNImage from '@/components/RNImage';
import { useHomePageData } from '@/hooks/useHomePageData';
import { useSendMessage } from '@/hooks/useSendMessage';
import TWS from '@/TWS';
import { EntryPoint, EntryPointType } from '@/types';

/**
 * 头部问候语与Banner组件
 * 基于主页数据Hook渲染问候语、天气信息和Banner
 */
const GreetingBanner: React.FC = () => {
    const { weatherGreeting, banner, isLoading } = useHomePageData();
    const { send } = useSendMessage();

    // 天气图标映射
    const weatherIcon = useMemo(() => {
        if (!weatherGreeting?.weatherType) {
            return NetImages.defaultIcon;
        }

        switch (weatherGreeting.weatherType) {
            case 'snow':
                return NetImages.snowIcon || NetImages.defaultIcon;
            case 'rain':
                return NetImages.rainIcon || NetImages.defaultIcon;
            case 'hot':
                return NetImages.hotIcon || NetImages.defaultIcon;
            case 'cold':
                return NetImages.coldIcon || NetImages.defaultIcon;
            case 'default':
            default:
                return NetImages.defaultIcon;
        }
    }, [weatherGreeting?.weatherType]);

    // Banner点击处理
    const handleBannerPress = () => {
        if (banner?.triggerQuestion) {
            send(
                { content: banner.triggerQuestion },
                EntryPointType.USER,
                EntryPoint.banner,
            );
        }
    };

    // 判断显示逻辑
    const shouldShowBanner = banner?.bannerContent && banner?.bannerButton;
    const shouldShowGreeting =
        weatherGreeting?.greeting || weatherGreeting?.weatherTips;
    // Banner只与weatherTips互斥，与greeting可以共存
    const shouldShowWeatherTips =
        !shouldShowBanner && weatherGreeting?.weatherTips;

    // 渲染Banner类型标识（内联显示）
    const renderBannerTypeIcon = () => {
        if (!banner?.type || banner.type === 'default') {
            return null;
        }

        if (banner.type === 'new') {
            return (
                <LinearGradient
                    style={styles.newBadge}
                    colors={['#4021FF', '#752FFF']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                >
                    <Text style={styles.newBadgeText}>新</Text>
                </LinearGradient>
            );
        }

        if (banner.type === 'notice') {
            return (
                <Icon
                    type={'info-circle-o'}
                    size={16}
                    tintColor={'#222'}
                    style={[
                        styles.noticeIcon,
                        { transform: [{ rotate: '180deg' }] },
                    ]}
                />
            );
        }

        return null;
    };

    // 加载状态
    if (isLoading) {
        return null;
    }

    return (
        <View style={styles.container}>
            {/* 天气问候语区域 - greeting部分始终显示，weatherTips与Banner互斥 */}
            {shouldShowGreeting ? (
                <View style={styles.greetingSection}>
                    {weatherIcon ? (
                        <RNImage
                            source={{ uri: weatherIcon }}
                            style={styles.weatherIcon}
                        />
                    ) : null}

                    {weatherGreeting.greeting ? (
                        <Text style={styles.greetingText}>
                            {weatherGreeting.greeting}
                        </Text>
                    ) : null}

                    {/* weatherTips 只在没有Banner时显示 */}
                    {shouldShowWeatherTips ? (
                        <View style={[TWS.row(), { alignItems: 'center' }]}>
                            <RNImage
                                source={NetImages.star}
                                style={{ width: 10, marginRight: 4 }}
                            />
                            <Text style={styles.weatherTips}>
                                {weatherGreeting.weatherTips}
                            </Text>
                        </View>
                    ) : null}
                </View>
            ) : null}

            {/* Banner区域 - 优先级更高 */}
            {shouldShowBanner ? (
                <TouchableOpacity
                    style={styles.bannerContainer}
                    onPress={handleBannerPress}
                    activeOpacity={0.8}
                >
                    <View style={styles.bannerContent}>
                        {/* 左侧：类型标识 + 文本内容 */}
                        <View style={styles.bannerLeftContent}>
                            {renderBannerTypeIcon()}
                            <Text style={styles.bannerText}>
                                {banner.bannerContent}
                            </Text>
                        </View>

                        {/* 右侧：渐变按钮 */}
                        <LinearGradient
                            colors={['#4021FF', '#752FFF']}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 0 }}
                            style={styles.bannerButton}
                        >
                            <Text style={styles.bannerButtonText}>
                                {banner.bannerButton}
                            </Text>
                        </LinearGradient>
                    </View>
                </TouchableOpacity>
            ) : null}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        alignItems: 'center',
        paddingHorizontal: 12,
    },
    loadingContainer: {
        paddingVertical: 24,
    },
    loadingText: {
        fontSize: 13,
        color: '#999',
    },
    greetingSection: {
        alignItems: 'center',
        marginBottom: 16,
    },
    weatherIcon: {
        height: 160,
    },
    greetingText: {
        fontSize: 20,
        fontWeight: '700',
        color: '#222',
        textAlign: 'center',
        marginBottom: 6,
        marginTop: -10,
    },
    weatherTips: {
        fontSize: 12,
        color: '#666',
        textAlign: 'center',
    },
    bannerContainer: {
        width: '100%',
        backgroundColor: '#FFFFFF',
        borderRadius: 10,
        padding: 12,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
    },
    bannerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    bannerLeftContent: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'center',
    },
    bannerText: {
        flex: 1,
        fontSize: 13,
        color: '#333',
        lineHeight: 18,
        marginRight: 10,
    },
    bannerButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 14,
        minHeight: 28,
        justifyContent: 'center',
        alignItems: 'center',
    },
    bannerButtonText: {
        fontSize: 12,
        fontWeight: '600',
        color: '#FFFFFF',
    },
    newBadge: {
        backgroundColor: '#6B46C1',
        borderRadius: 4,
        paddingVertical: 2,
        minWidth: 16,
        height: 16,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 8,
    },
    newBadgeText: {
        fontSize: 10,
        fontWeight: '600',
        color: '#FFFFFF',
    },
    noticeIcon: {
        marginRight: 8,
    },
});

export default GreetingBanner;
