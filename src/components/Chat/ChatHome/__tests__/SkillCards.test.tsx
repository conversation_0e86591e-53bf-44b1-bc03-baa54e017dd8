import { render, fireEvent } from '@testing-library/react-native';
import React from 'react';
import { Dimensions } from '@mrn/react-native';

import SkillCards from '../SkillCards';

// Mock dependencies
jest.mock('../../../../hooks/useHomePageData', () => ({
    useHomePageData: () => ({
        skillGroups: [
            {
                type: 'group',
                name: '测试技能组',
                skills: [
                    { id: '1', content: '技能1', link: 'http://example.com/1.jpg' },
                    { id: '2', content: '技能2', link: 'http://example.com/2.jpg' },
                    { id: '3', content: '技能3', link: 'http://example.com/3.jpg' },
                    { id: '4', content: '技能4', link: 'http://example.com/4.jpg' },
                    { id: '5', content: '技能5', link: 'http://example.com/5.jpg' },
                    { id: '6', content: '技能6', link: 'http://example.com/6.jpg' },
                ],
            },
        ],
        isLoading: false,
    }),
}));

jest.mock('../../../../hooks/useSendMessage', () => ({
    useSendMessage: () => ({ send: jest.fn() }),
}));

jest.mock('../../../../store/uiState', () => ({
    useUiState: () => ({
        setSkillCardPosition: jest.fn(),
    }),
}));

jest.mock('../../../../hooks/useLayout', () => ({
    useLayout: () => ({
        onLayout: jest.fn(),
        layout: { x: 0, y: 0, width: 100, height: 100 },
    }),
}));

// Mock LinearGradient
jest.mock('@mrn/react-native-linear-gradient', () => ({
    LinearGradient: ({ children, ...props }: any) => {
        const { View } = require('@mrn/react-native');
        return <View {...props}>{children}</View>;
    },
}));

// Mock RNImage
jest.mock('../../../../components/RNImage', () => {
    const { View } = require('@mrn/react-native');
    return ({ style, ...props }: any) => <View style={style} {...props} />;
});

// Mock Icon
jest.mock('@roo/roo-rn', () => ({
    Icon: ({ ...props }: any) => {
        const { View } = require('@mrn/react-native');
        return <View {...props} />;
    },
}));

describe('SkillCards - 平铺布局分页测试', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Mock screen dimensions
        jest.spyOn(Dimensions, 'get').mockReturnValue({
            width: 375,
            height: 812,
            scale: 2,
            fontScale: 1,
        });
    });

    it('应该正确渲染平铺布局（只有一个技能组）', () => {
        const { getByText } = render(<SkillCards />);
        
        // 验证技能组标题
        expect(getByText('测试技能组')).toBeTruthy();
        
        // 验证技能项
        expect(getByText('技能1')).toBeTruthy();
        expect(getByText('技能2')).toBeTruthy();
        expect(getByText('技能3')).toBeTruthy();
        expect(getByText('技能4')).toBeTruthy();
    });

    it('应该正确处理分页逻辑', () => {
        const { getByTestId } = render(<SkillCards />);
        
        // 模拟滚动到第二页
        const scrollView = getByTestId('tiled-scroll-view');
        fireEvent.scroll(scrollView, {
            nativeEvent: {
                contentOffset: { x: 343, y: 0 }, // screenWidth - 32 = 343
            },
        });
        
        // 验证分页指示器应该显示第二页
        // 这里可以添加更多的验证逻辑
    });

    it('应该正确处理技能数量少于4个的情况', () => {
        // 重新mock数据，只有2个技能
        jest.doMock('../../../../hooks/useHomePageData', () => ({
            useHomePageData: () => ({
                skillGroups: [
                    {
                        type: 'group',
                        name: '少量技能组',
                        skills: [
                            { id: '1', content: '技能1', link: 'http://example.com/1.jpg' },
                            { id: '2', content: '技能2', link: 'http://example.com/2.jpg' },
                        ],
                    },
                ],
                isLoading: false,
            }),
        }));

        const { getByText } = render(<SkillCards />);
        
        // 验证技能项正确显示
        expect(getByText('技能1')).toBeTruthy();
        expect(getByText('技能2')).toBeTruthy();
    });

    it('应该在加载状态时返回null', () => {
        jest.doMock('../../../../hooks/useHomePageData', () => ({
            useHomePageData: () => ({
                skillGroups: [],
                isLoading: true,
            }),
        }));

        const { container } = render(<SkillCards />);
        expect(container.children.length).toBe(0);
    });

    it('应该在没有技能组时返回null', () => {
        jest.doMock('../../../../hooks/useHomePageData', () => ({
            useHomePageData: () => ({
                skillGroups: [],
                isLoading: false,
            }),
        }));

        const { container } = render(<SkillCards />);
        expect(container.children.length).toBe(0);
    });
});
