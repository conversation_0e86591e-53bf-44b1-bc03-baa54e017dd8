import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    SectionList,
    Animated,
    ScrollView,
} from '@mrn/react-native';
import { LinearGradient } from '@mrn/react-native-linear-gradient';
import { Toast } from '@roo/roo-rn';
import dayjs from 'dayjs';
import React, { useState, useMemo, useEffect } from 'react';

import AICallItem from './AICallItem';
// 导入图标
import { PoiDiagnosisTaskItem } from './TaskListItem';
import {
    TaskItem,
    clearTaskRedDot,
    TaskJob,
    useTaskListApi,
} from '../../api/taskApi';
import { useBizInfo } from '../../hooks/useBizInfo';
import Condition from '../Condition/Condition';

import { OperationType } from '@/types/message';

// 扩展TaskItem类型，包含createTime
interface TaskItemWithTime extends TaskItem {
    createTime: string;
    taskType?: 'PoiDiagnosis' | 'AiCall';
    agentName?: string;
    jobId?: string;
    poiNum?: number;
}

interface TaskListContentProps {
    /** 发送消息回调 */
    onSendMessage: (content: string) => void;
}

// 任务类型枚举和筛选选项
const filterOptions = [
    { key: 'ALL', label: '全部' },
    { key: 'PoiDiagnosis', label: '商家诊断' },
    { key: 'AiCall', label: '外呼任务' },
];

// 格式化日期显示
const formatDateDisplay = (dateStr: string) => {
    const targetDate = dayjs(dateStr);
    const today = dayjs();
    const yesterday = today.subtract(1, 'day');

    if (targetDate.isSame(today, 'day')) {
        return '今日';
    } else if (targetDate.isSame(yesterday, 'day')) {
        return '昨日';
    } else {
        return targetDate.format('M月D日');
    }
};

/**
 * 任务列表内容组件 - 从TaskListDrawer提取的可复用主体部分
 */
export const TaskListContent: React.FC<TaskListContentProps> = ({
    onSendMessage,
}) => {
    const [taskData, setTaskData] = useState<TaskJob[]>([]);
    const [selectedFilter, setSelectedFilter] = useState<string>('ALL');
    const [loading, setLoading] = useState(false);
    const { bizId } = useBizInfo();
    const { getTaskList } = useTaskListApi();

    // 获取任务列表数据
    const fetchTaskList = async (filterType?: string) => {
        try {
            setLoading(true);
            const taskType = filterType || selectedFilter;
            const data = await getTaskList(
                taskType as 'ALL' | 'PoiDiagnosis' | 'AiCall',
                bizId,
            );
            if (data?.data?.jobList) {
                setTaskData(data.data.jobList);
            } else {
                setTaskData([]);
            }
        } catch (error) {
            console.error('获取任务列表失败:', error);
            Toast.open('获取任务列表失败');
            setTaskData([]);
        } finally {
            setLoading(false);
        }
    };

    // 处理查看结果 - 重载以支持外呼任务
    const handleViewResult = (item: any) => {
        // 乐观更新：立即清除红点
        if (item.showRedDot) {
            setTaskData((prevData) => {
                if (!prevData) {
                    return prevData;
                }

                const updatedJobList = prevData.map((job) => ({
                    ...job,
                    itemList: job.itemList.map((taskItem) =>
                        taskItem.id === item.id
                            ? { ...taskItem, showRedDot: false }
                            : taskItem,
                    ),
                }));

                return updatedJobList;
            });

            // 后台调用清除红点API（不关心成功与否）
            clearTaskRedDot(item.id);
        }

        // 如果是TaskJob类型（外呼任务），直接处理
        if ('type' in item) {
            const job = item as TaskJob;
            if (job.type === 'AiCall') {
                const firstItem = job.itemList?.[0];
                if (
                    firstItem?.operationType == OperationType.JUMP_LINK &&
                    firstItem?.content
                ) {
                    console.log('跳转到URL:', firstItem.content);
                } else if (
                    firstItem?.operationType == OperationType.SEND_MESSAGE &&
                    firstItem?.content
                ) {
                    onSendMessage(firstItem.content);
                }
            }
            return;
        }

        // 查找对应的任务
        const job = taskData.find((job) => {
            if (job.type === 'PoiDiagnosis') {
                // 商家诊断任务：通过itemList中的poiId匹配
                return job.itemList?.some(
                    (subItem) => subItem.poiId === item.poiId,
                );
            } else if (job.type === 'AiCall') {
                // 外呼任务：通过jobId匹配
                return job.jobId === item.jobId;
            }
            return false;
        });

        if (!job) {
            return;
        }

        if (job.type === 'PoiDiagnosis') {
            // 商家诊断任务处理：使用itemList中的数据，匹配PC端逻辑
            if (!(item.status === 'success' || item.status === 'fail')) {
                return;
            }
            onSendMessage(item.content);
        }
    };

    // 处理筛选选择
    const handleFilterSelect = (filterKey: string) => {
        setSelectedFilter(filterKey);
        fetchTaskList(filterKey);
    };
    useEffect(() => {
        handleFilterSelect(selectedFilter);
    }, []);

    const sections = useMemo(() => {
        return taskData.map((job) => {
            return {
                title: dayjs(job.createTime).format('YYYY-MM-DD HH:mm:ss'),
                type: job.type,
                data: [job],
                createTime: dayjs(job.createTime).format('YYYY-MM-DD HH:mm:ss'),
                jobData: job, // 保存原始job数据
            };
        });
    }, [taskData]);

    const renderEmptyState = () => (
        <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
                {loading ? '加载中...' : '暂无任务数据'}
            </Text>
        </View>
    );

    // 渲染筛选器
    const renderFilter = () => (
        <View style={styles.filterContainer}>
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={styles.filterScrollContent}
            >
                {filterOptions.map((option) => (
                    <Condition
                        condition={[selectedFilter === option.key, true]}
                    >
                        <TouchableOpacity
                            onPress={() => handleFilterSelect(option.key)}
                            style={[
                                {
                                    borderRadius: 6,
                                    overflow: 'hidden',
                                    marginRight: 12,
                                },
                            ]}
                        >
                            <LinearGradient
                                colors={['#4021FF', '#752FFF']}
                                style={[
                                    {
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        paddingHorizontal: 12,
                                        paddingVertical: 6,
                                    },
                                ]}
                            >
                                <Text
                                    style={{
                                        fontSize: 14,
                                        color: '#FFF',
                                        fontWeight: '500',
                                    }}
                                >
                                    {option.label}
                                </Text>
                            </LinearGradient>
                        </TouchableOpacity>
                        <TouchableOpacity
                            key={option.key}
                            style={[
                                styles.filterButton,
                                selectedFilter === option.key &&
                                    styles.filterButtonActive,
                            ]}
                            onPress={() => handleFilterSelect(option.key)}
                        >
                            <Text
                                style={[
                                    styles.filterButtonText,
                                    selectedFilter === option.key &&
                                        styles.filterButtonTextActive,
                                ]}
                            >
                                {option.label}
                            </Text>
                        </TouchableOpacity>
                    </Condition>
                ))}
            </ScrollView>
        </View>
    );

    return (
        <View style={{ flex: 1, paddingBottom: 20 }}>
            <Animated.View style={[styles.drawer]}>
                {/* 筛选器 */}
                {renderFilter()}

                {/* 任务列表 */}
                <View style={styles.listContainer}>
                    <SectionList
                        sections={sections}
                        stickySectionHeadersEnabled={false}
                        renderItem={({ item, section }) => {
                            // 找到当前section在sections数组中的索引
                            const sectionIndex = sections.findIndex(
                                (s) => s === section,
                            );

                            const itemView =
                                section.type === 'AiCall' ? (
                                    <AICallItem
                                        item={item as TaskJob}
                                        onViewResult={handleViewResult}
                                    />
                                ) : (
                                    <View
                                        style={{
                                            overflow: 'hidden',
                                            borderColor: '#fff',
                                            borderWidth: 2,
                                            borderBottomWidth: 0,
                                            marginRight: 12,
                                            borderRadius: 8,
                                            flex: 1,
                                        }}
                                    >
                                        <LinearGradient
                                            colors={['#f5f6fa', '#fff']}
                                            style={{
                                                flex: 1,
                                                borderRadius: 8,
                                            }}
                                        >
                                            <Text
                                                style={{
                                                    fontSize: 16,
                                                    fontWeight: '500',
                                                    color: '#222222',
                                                    marginTop: 12,
                                                    marginLeft: 12,
                                                }}
                                            >
                                                商家诊断
                                            </Text>
                                            {item.itemList?.map((item) => (
                                                <PoiDiagnosisTaskItem
                                                    item={
                                                        item as TaskItemWithTime
                                                    }
                                                    onViewResult={
                                                        handleViewResult
                                                    }
                                                />
                                            ))}
                                        </LinearGradient>
                                    </View>
                                );
                            return (
                                <View style={styles.timeLineItemContainer}>
                                    <View
                                        style={[
                                            styles.timeLineVerticalLine,
                                            {
                                                opacity:
                                                    sectionIndex !==
                                                    sections.length - 1
                                                        ? 1
                                                        : 0,
                                            },
                                        ]}
                                    />

                                    {itemView}
                                </View>
                            );
                        }}
                        renderSectionHeader={({ section: { title } }) => {
                            return (
                                <View style={styles.sectionHeader}>
                                    <View style={styles.timeLineContainer}>
                                        <View style={styles.timeLineCircle}>
                                            <View
                                                style={
                                                    styles.timeLineCircleInner
                                                }
                                            />
                                        </View>
                                    </View>
                                    <View style={styles.sectionTimeContainer}>
                                        <Text style={styles.sectionDate}>
                                            {formatDateDisplay(title)}
                                        </Text>
                                        <Text style={styles.sectionTime}>
                                            {dayjs(title).format('H:mm')}
                                        </Text>
                                    </View>
                                </View>
                            );
                        }}
                        keyExtractor={(item, index) =>
                            `${
                                (item as any).jobId || (item as any).poiId
                            }_${index}`
                        }
                        showsVerticalScrollIndicator={false}
                        ListEmptyComponent={renderEmptyState}
                    />
                </View>
            </Animated.View>
        </View>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flexDirection: 'row',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    overlayBackground: {
        flex: 1,
    },
    overlayTouchable: {
        flex: 1,
    },
    drawer: {
        elevation: 5,
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 16,
        paddingTop: 20,
    },
    title: {
        fontSize: 18,
        fontWeight: '600',
        color: '#222222',
    },
    closeButton: {
        padding: 4,
    },
    filterContainer: {
        paddingHorizontal: 16,
        marginBottom: 12,
    },
    filterScrollContent: {
        paddingRight: 16,
    },
    filterButton: {
        paddingHorizontal: 16,
        paddingVertical: 6,
        borderRadius: 6,
        backgroundColor: '#fff',
        marginRight: 12,
        minWidth: 60,
        alignItems: 'center',
    },
    filterButtonActive: {
        backgroundColor: '#4021FF',
    },
    filterButtonText: {
        fontSize: 14,
        color: '#666666',
        fontWeight: '400',
    },
    filterButtonTextActive: {
        color: '#FFFFFF',
        fontWeight: '500',
    },
    statsContainer: {
        paddingHorizontal: 16,
        marginBottom: 16,
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    statItem: {
        alignItems: 'center',
        justifyContent: 'center',
        flex: 1,
        backgroundColor: '#F9FAFC',
        borderRadius: 8,
        paddingVertical: 12,
        marginHorizontal: 4,
    },
    statValue: {
        fontSize: 22,
        fontWeight: 'bold',
        marginTop: 4,
        color: '#222',
    },
    statLabel: {
        fontSize: 14,
        color: '#666',
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 6,
    },
    sectionTime: {
        fontSize: 18,
        color: '#222222',
        fontFamily: 'PingFang SC',
        fontWeight: '600',
        lineHeight: 24,
        textAlign: 'center',
    },
    sectionTitle: {
        fontSize: 14,
        color: '#222222',
        fontWeight: '500',
    },
    timeLineItemContainer: {
        flexDirection: 'row',
    },
    timeLineContainer: {
        alignItems: 'center',
        width: 40, // 根据需要调整
        marginLeft: -16,
    },
    timeLineCircle: {
        width: 16,
        height: 16,
        borderRadius: 8,
        backgroundColor: '#4021FF', // 根据需要调整
        marginBottom: 4,
        alignItems: 'center',
        justifyContent: 'center',
    },
    timeLineCircleInner: {
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: '#fff', // 根据需要调整
    },
    timeLineVerticalLine: {
        width: 1,
        backgroundColor: '#e2e2e2', // 根据需要调整
        marginHorizontal: 18,
    },
    listContainer: {
        flex: 1,
    },
    loadingContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    loadingText: {
        fontSize: 14,
        color: '#999999',
    },
    emptyContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 14,
        color: '#999999',
        marginTop: 12,
    },
    iconContainer: {
        padding: 4,
        alignItems: 'center',
        justifyContent: 'center',
    },
    sectionTimeContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    sectionDate: {
        fontSize: 18,
        color: '#222222',
        fontFamily: 'PingFang SC',
        fontWeight: '600',
        lineHeight: 24,
        textAlign: 'center',
        marginRight: 8,
    },
});
