import {
    Keyboard,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from '@mrn/react-native';
import { LinearGradient } from '@mrn/react-native-linear-gradient';
import { Icon } from '@roo/roo-rn';
import { useRequest } from 'ahooks';
import React, { useState, Fragment } from 'react';

import RNImage from './RNImage';
import { useBizInfo } from '../hooks/useBizInfo';
import useCallerRequest from '../hooks/useCallerRequest';
import useMessage from '../hooks/useMessage';
import { useSendMessage } from '../hooks/useSendMessage';
import { EntryPointType } from '../types';

import NetImages from '@/assets/images/homeRefactor';
import { COLORS } from '@/consts';
import useAbsolutePosition from '@/hooks/useAbsolutePosition';
import { useUiState } from '@/store/uiState';
import TWS from '@/TWS';
import { trackEvent, TrackEventType } from '@/utils/track';

const styles = StyleSheet.create({
    container: {
        marginTop: 2,
        paddingTop: 16,
        paddingBottom: 12,
        borderRadius: 10.5,
        backgroundColor: '#fff',
        zIndex: 140,
        position: 'absolute',
        left: 12,
        right: 12,
        borderColor: '#fff',
        borderWidth: 2,
    },
    scrollView: {
        paddingHorizontal: 16,
    },
    title: {
        paddingHorizontal: 16,
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 7,
    },
    image: {
        width: 18,
    },
    item: {
        paddingVertical: 6,
        flexDirection: 'row',
        alignItems: 'center',
    },
    itemBorder: {
        borderBottomColor: '#eee',
        borderBottomWidth: 0.5,
    },
    highlight: {
        color: COLORS.LINK,
    },
    shadow: {
        ...Platform.select({
            ios: {
                shadowColor: '#171717',
                shadowOffset: {
                    width: 2,
                    height: 2,
                },
                shadowOpacity: 0.9,
                shadowRadius: 8,
            },
            android: {
                elevation: 5,
                zIndex: 5,
                backgroundColor: '#fff',
            },
        }),
    },
});

const InputAssociation = () => {
    const { text, clear } = useMessage((state) => state.input);

    const [visible, setVisible] = useState(false);
    const { send } = useSendMessage();
    const checkIsPolling = useMessage((state) => state.checkIsPolling);
    const focused = useMessage((state) => state.input.focused);
    const { bizId } = useBizInfo();
    const { showHome } = useUiState();

    const callerRequest = useCallerRequest();
    const fetchAssociation = async () => {
        // 修正逻辑：当文本长度小于2或者没有聚焦时，不触发
        if (text.length < 2 && (!focused || !showHome)) {
            console.log('InputAssociation: 条件不满足，不触发联想');
            return;
        }

        setVisible(true);
        const res = await callerRequest.get(
            '/bee/v1/bdaiassistant/getRelatedQuestion',
            { input: text, bizId },
            { silent: true },
        );

        if (res.code !== 0) {
            console.log('InputAssociation: API返回错误', res.code, res.msg);
            return;
        }

        return res.data.questions;
    };

    const { data = [], mutate } = useRequest(fetchAssociation, {
        refreshDeps: [text, focused],
        throttleWait: 300,
    });

    const absolutePosition = useAbsolutePosition();

    const onPress = (content: string) => {
        if (checkIsPolling()) {
            return;
        }
        mutate([]);
        clear();
        trackEvent(
            'chat_association',
            { question: text, msg_content: content },
            TrackEventType.MC,
            {},
        );
        send(content, EntryPointType.ASSOCIATION);
        Keyboard.dismiss();
    };

    const file = useMessage((state) => state.file);
    if (!visible || !data.length || file.length > 0) {
        return null;
    }

    return (
        <LinearGradient
            style={[styles.container, { bottom: absolutePosition.bottom }]}
            colors={['#EDE7FF', '#FFFFFF']}
            start={{ x: 0, y: 0 }}
            end={{ x: 0.2, y: 0.1 }}
            angle={166}
        >
            <View style={styles.title}>
                <View style={[TWS.row(), { alignItems: 'center' }]}>
                    <RNImage
                        source={{ uri: NetImages.tipsIcon }}
                        style={[styles.image, { marginRight: 6 }]}
                    />
                    <Text style={{ fontSize: 16, fontWeight: 'bold' }}>
                        猜你想问
                    </Text>
                </View>
                <TouchableOpacity onPress={() => setVisible(false)}>
                    <Icon size={14} type="close" tintColor="#000000" />
                </TouchableOpacity>
            </View>

            <ScrollView
                contentContainerStyle={styles.scrollView}
                keyboardShouldPersistTaps={'handled'}
            >
                {data.map((d) => (
                    <TouchableOpacity
                        style={[styles.item]}
                        key={d}
                        onPress={() => onPress(d)}
                    >
                        <Text
                            numberOfLines={1}
                            style={{ marginRight: 2, fontSize: 16 }}
                        >
                            {d.split(text).map((it, i, arr) => (
                                <Fragment key={`${it}_${i}`}>
                                    <Text>{it}</Text>
                                    {i < arr.length - 1 ? (
                                        <Text style={styles.highlight}>
                                            {text}
                                        </Text>
                                    ) : null}
                                </Fragment>
                            ))}
                        </Text>
                        <RNImage
                            source={NetImages.arrowRight}
                            style={{ width: 10 }}
                        />
                    </TouchableOpacity>
                ))}
            </ScrollView>
        </LinearGradient>
    );
};

export default InputAssociation;
